# 多业务参数管理

## 背景

完整交易参数分为三个部分，分别由三个团队维护：参数（业务管理）、费率(支付)、状态(风控)，其中风控状态管理比较独立，并且和其余两个部分没有耦合，所以此次不讨论。

早期业务比较简单，只有一套参数、一套费率，两者也不耦合，管理起来都比较简单；

随着多业务的概念出现这一情况发生了巨变

已知:收钱吧当前将旗下的各种产品定义为业务方如
越吃越优惠卡,微信B2b,富友一体化外卡,摄影,零售版自营外卖,无忧码,富友一体化刷卡,手机POS外卡,外卡刷卡收款
,零售收银系统,校园外卖,银行合作,扫码点单,支付业务

其中部分业务如(扫码点单,支付业务,会员储值,自营外卖,校园外卖,线上收款,跨城收款,零售版自营外卖)需要配置不同对点交易参数,以便在交易的时候自由选择

支付业务(又可以叫做移动支付业务)参数存放在merchant_config表,其他业务(又可以称为多业务,如扫码点单,会员储值,自营外卖,校园外卖,线上收款,跨城收款,零售版自营外卖)会存在merchant_app_config表.(merchant_app_config和merchant_config结构是一样的,merchant_app_config多了一个"app_id"字段)

## 各种规则

### 收单机构汇总分类

为了熟悉各种规则,首先需要了解各种收单机构

| 收单机构类型 | 费率生效类型 (fee_effective_type) | acquirer | 收单机构名称 | 支付清算组定义的provider | 交易类型 (trade_type) | 清算类型(clear_type) |
|-------------|----------------------------------|----------|-------------|------------------------|---------------------|---------------------|
| 3支付源直连机构 | 2 以收单机构为准 | alipay | 支付宝 | 2 | 2直连交易 | 2直接清算 |
| 3支付源直连机构 | 2 以收单机构为准 | weixin | 微信 | 3 | 2直连交易 | 2直接清算 |
| 3支付源直连机构 | 2 以收单机构为准 | bestpay | 翼支付 | 18 | 2直连交易 | 2直接清算 |
| 2银行机构 | 1 以收钱吧为准 | psbc | 邮储银行 | 1023 | 1间连交易 | 2直接清算 |
| 2银行机构 | 1 以收钱吧为准 | cgb | 广发银行 | 1024 | 1间连交易 | 2直接清算 |
| 2银行机构 | 1 以收钱吧为准 | ccb | 建设银行 | 1026 | 1间连交易 | 2直接清算 |
| 2银行机构 | 1 以收钱吧为准 | hxb | 华夏银行 | 1028 | 1间连交易 | 2直接清算 |
| 2银行机构 | 1 以收钱吧为准 | icbc | 工商银行 | 1030 | 1间连交易 | 2直接清算 |
| 2银行机构 | 1 以收钱吧为准 | pab | 平安银行 | 1040 | 1间连交易 | 2直接清算 |
| 2银行机构 | 1 以收钱吧为准 | zjtlcb | 泰隆银行 | 1043 | 1间连交易 | 2直接清算 |
| 2银行机构 | 1 以收钱吧为准 | fjnx | 福建农信社 | 1044 | 1间连交易 | 2直接清算 |
| 2银行机构 | 1 以收钱吧为准 | jsb | 江苏银行 | 1047 | 1间连交易 | 2直接清算 |
| 2银行机构 | 1 以收钱吧为准 | lzb | 泸州银行 | 1049 | 1间连交易 | 2直接清算 |
| 2银行机构 | 1 以收钱吧为准 | abc | 中国农业银行 | 1041 | 1间连交易 | 2直接清算 |
| 2银行机构 | 1 以收钱吧为准 | cmbc | 民生银行 | 1010 | 1间连交易 | 2直接清算 |
| 1三方机构 | 1 以收钱吧为准 | lkl | 拉卡拉 | 1002 | 1间连交易 | 1间接清算 |
| 1三方机构 | 1 以收钱吧为准 | cib | 上海兴业 | 1001 | 2直连交易 | 2直接清算 |
| 1三方机构 | 1 以收钱吧为准 | tonglian | 通联 | 1020 | 1间连交易 | 1间接清算 |
| 1三方机构 | 1 以收钱吧为准 | ums | 银联商务 | 1018 | 1间连交易 | 1间接清算 |
| 1三方机构 | 1 以收钱吧为准 | lklV3 | 拉卡拉V3 | 1032 | 1间连交易 | 1间接清算 |
| 1三方机构 | 1 以收钱吧为准 | haike | 海科 | 1037 | 1间连交易 | 1间接清算 |
| 1三方机构 | 1 以收钱吧为准 | guotong | 国通星驿 | 1048 | 1间连交易 | 1间接清算 |
| 1三方机构 | 2 以收单机构为准 | tonglianV2 | 通联收银宝 | 1035 | 2直连交易 | 1间接清算 |
| 1三方机构 | 2 以收单机构为准 | fuyou | 富友 | 1038 | 1间连交易 | 1间接清算 |
| 1三方机构 | 2 以收单机构为准 | umb | 中投科信 | 1050 | 2直连交易 | 2直接清算 |

目前默认三方机构(由于都是间接清算所以又可以称为间连收单机构)都可以支持分账,能支持分账也就支持多业务(除了移动支付业务外其他的业务方都叫多业务),而银行机构不支持多业务,支付源直连机构也不支持分账也就不支持多业务了.但是运营可以通过审批强制将多业务配置在银行通道.这就是收单机构和多业务的关系了,同时部分收单机构费率以收单机构为准,所以在配置多业务的时候一定要保证商户对应的所有多业务费率都是一致的.

### 多业务目前支持的业务方

当通过apollo进行管理业务方能否开通多业务,目前已经开通的业务方有

| 业务方 | forceCombo | paywayCombo | 说明 |
|--------|------------|-------------|------|
| 扫码点单 | false | false | 标准多业务配置 |
| 会员储值 | false | false | 标准多业务配置 |
| 自营外卖 | false | false | 标准多业务配置 |
| 校园外卖 | false | false | 标准多业务配置 |
| 跨城收款 | false | false | 标准多业务配置 |
| 零售版自营外卖 | false | false | 标准多业务配置 |
| 线上收款 | true | true | 必须使用专有套餐，按业务方传参确定payway |
| 无忧收款 | true | true | 必须使用专有套餐，按业务方传参确定payway |

可以看到某些特殊业务方如(线上和无忧收款forceCombo为true)无论什么场景都要使用专有套餐,
paywayCombo为true:不会统一配置支付宝微信和云闪付而是按照业务方传参来确定开通的payway

### 多业务规则

#### 多业务开通规则

**商户准入规则：**
商户必须已完成进件且进件状态为成功
泰隆银行商户不支持开通智慧经营业务
在多业务白名单中的商户不允许开通智慧经营
商户必须有可用的支付宝/微信交易参数

**收单机构选择规则：**
当前收单机构支付多业务优先使用商户当前的收单机构
当前收单机构不支持多业务时,选择新收单机构需遵循:
如果其他多业务已存在
则选择已经存在业务对应的收单机构且商户当前银行卡和收单机构一致
其他多业务不存时
入网成功的收单机构
且商户当前银行卡和收单机构一致
去除富友(费率以富友为准不支持多个费率)
微信和支付宝实名
按照系统配置的收单机构优先级顺序选择(海科>拉卡拉>富友)

**参数配置规则：**
当使用现有收单机构时:
直接复用移动支付业务的交易参数
避免重新获取参数导致使用了不同时间创建的子商户号
当使用新收单机构时:
获取该收单机构的默认交易参数(有多个支付宝交易参数优先获取实名过的子商户号，都未实名则获取最新的一个)
参数必须包含支付宝和微信的配置

**费率套餐规则：**
使用apollo中的 appId_subBiz 中对应的多业务配置
以下情况直接复用移动支付业务的费率套餐:
当前多业务没有配置特殊套餐(forceCombo为false或者没有forceCombo字段)且移动支付业务使用三方收单机构
其他情况下的费率配置:
支付业务使用银行机构或者直连
 多业务单独配置费率
支付业务使用三方机构但forceCombo配置为true
 多业务单独配置费率

**多通道管控规则：**
同一个商户只允许使用一个三方机构

**状态同步规则：**
开通完成后需要同步更新:
将商户在收单机构的状态设置为有效
通过消息通知交易多业务开通了

#### 多业务开通成功后子商户实名认证

未实名的账户通常会有较低的交易限额，影响日常经营中的收款和付款操作,所以子商户实名以后,需要替换多业务中对应的支付宝/微信参数这样保证交易权限最大化
然后有些业务方如线上和无忧收款必须使用特殊场景(merchant_provider_params中的wx_use_type)的微信或者支付宝子商户号,所以这种场景又不能做替换
同时对于一些业务方,如T9刷卡或者手机POS外卡,这些交易并不和支付宝或者微信交互,而是直接和三方机构直接交互调用的是三方机构的接口,所以也不需要更新支付宝或微信,所以就衍生出业务方交易的时候需要哪些payway支撑完成交易

| 业务方 | 需要哪些payway完成交易 |
|--------|----------------------|
| T9刷卡 | 直接和三方机构交互 |
| 手机POS外卡 | 直接和三方机构交互 |
| 其他 | 支付宝/微信/云闪付 |

#### 多业务开通成功切换通道

**指定切换多业务**
当通过审批强制指定多业务切换到银行机构,则需要取消之前配置的多业务套餐和费率,如储值从三方切换到华夏银行,则要取消储值配置的套餐和费率.

**切换支付业务**

当移动支付业务切回间连时取消其他业务配置的套餐,同时将merchant_config和merchant_app_config的数据都改成同样的.
当移动支付从三方切换到银行时,还要重新将多业务的费率套餐设置一遍

随着业务的发展，特别是支持多业务后，交易参数的管理就变得十分混乱，表现在

**1、存在多套参数和费率，多业务之间又相互影响。**

**2、有些场景参数和费率绑定**
1）银行业务(银行返利导致费率会普遍低于0.38)
2）支付源活动(现实高校活动,或者支付宝的各种计划会导致费率甚至为0)
3）线上业务(线上业务必须使用能用于线上的费率)
a. 这些参数变更的同时都要求费率也要一起变更
b. 有业务要求参数回切还要支持费率恢复
c. 参数和费率不能被其他业务使用，比如间联扫码不能使用线上子商户号,线上场景的子商户号只能用于线上业务且费率不可以改变,

在业务开通、关闭，参数切换的场景中都要小心维护参数和费率的正确性，以避免出现
1、业务和参数不匹配
2、业务和费率不匹配
3、参数和费率不匹配
4、都不匹配

所以希望能够有一套系统能够沉淀业务逻辑，负责计算业务、参数、费率的匹配关系，避免在业务中处理这个复杂的逻辑。

## 现状

```mermaid
graph TD
    A[商户] --> B[支付业务]
    A --> C[扫码点单]
    A --> D[会员储值]
    A --> E[其他多业务...]

    B --> F[merchant_config表]
    C --> G[merchant_app_config表]
    D --> G
    E --> G

    F --> H[参数管理逻辑]
    G --> H

    H --> I[费率管理逻辑]
    H --> J[状态管理逻辑]

    I --> K[业务开发需要了解所有逻辑]
    J --> K

    K --> L[容易出现匹配错误]
    L --> M[业务和参数不匹配]
    L --> N[业务和费率不匹配]
    L --> O[参数和费率不匹配]
    L --> P[都不匹配]

    style K fill:#ffcccc
    style L fill:#ffcccc
```

目前参数和费率的管理都在业务流程中处理，需要每个业务的开发都非常了解这套逻辑以及对其他业务可能造成的影响。

## 目标

```mermaid
graph TD
    A[商户] --> B[交易请求]
    B --> C[多业务参数管理系统]

    C --> D[业务方配置表]
    C --> E[收单机构能力表]
    C --> F[参数表]
    C --> G[指定配置表]

    D --> H[规则计算引擎]
    E --> H
    F --> H
    G --> H

    H --> I[计算业务、参数、费率匹配关系]
    I --> J[缓存计算结果]
    J --> K[返回正确的交易参数和费率]

    K --> L[交易执行]

    style C fill:#ccffcc
    style H fill:#ccffcc
    style I fill:#ccffcc

    M[不再需要merchant_app_config] --> N[简化业务流程]
    N --> O[减少开发复杂度]
```

在新的设计下，其实并不关心商户开了哪些业务，开业务的过程中也不需要配置merchan_app_config，一切都是交易时通过规则计算出来，当然计算结果可以缓存以提高性能.--(只是业务管理来推动目前看起来还是需要将交易参数落到数据库,但是当收单机构与子商户号一致的场景下,不需要要为某个业务单独配置)

## 商户数据逻辑结构

### 参数表

| 支付产品 | 收单机构 | 子商户号 | payway | 场景 | 套餐 | 是否默认 | 说明 |
|----------|----------|----------|--------|------|------|----------|------|
| 间联扫码 | 拉卡拉（默认） | WX1 | 3 | | | 否 | |
| | | WX2 | 3 | | | 否 | |
| | | WX3 | 3 | | 高校食堂套餐 | 是 | 微信高校食堂 |
| | | WX4 | 3 | 线上 | 线上套餐 | 是 | |
| | | ALI1 | 2 | | | 是 | |
| | | ALI2 | 2 | 线上 | 线上套餐 | 是 | |
| | | BANK1 | 21 | | 银行卡套餐 | 是 | |
| | 海科 | | | | | | |
| | 富友 | | | | | | |
| 华夏银行 | 华夏银行 | WX1 | 3 | | 华夏银行套餐 | 是 | |
| | | ALI1 | 2 | | 华夏银行套餐 | 是 | |
| 华夏银行高费率 | 华夏银行 | WX1 | 3 | | 华夏高费率套餐 | 是 | |
| | | ALI1 | 2 | | 华夏高费率套餐 | 是 | |
| 工商银行 | 工商银行 | WX1 | 3 | | 工商银行套餐 | 是 | |
| | | ALI1 | 2 | | 工商银行套餐 | 是 | |

### 指定配置表

| 业务方 | 指定产品 | 指定收单机构 | 指定套餐套餐 | payway | 场景 |
|--------|----------|--------------|--------------|--------|------|
| 支付业务 | 间联扫码 | 拉卡拉 | | 2 | |
| 支付业务 | 华夏银行 | 华夏银行 | | 3 | |
| 扫码点单 | 华夏银行高费率 | 华夏银行 | | 2,3 | |
| 银行卡收款 | 间联扫码 | 拉卡拉 | | 21 | |
| 无忧收款 | 间联扫码 | 拉卡拉 | | 2,3 | 线上 |

注意：指定配置表理论上应该由多业务参数计算逻辑计算出来。
但是也留了口子给特殊业务直接配置，配置时应该有两个强制校验逻辑  1）只能同时存在一个三方   2）不支持多费率的机构只能存在一套费率

### 公共元数据

#### 收单机构能力表

| 支付产品 | 收单机构 | 是否支持分账 | 是否支持多费率 | 类型 | 清算方式 | 是否允许切走 | ...... |
|----------|----------|--------------|----------------|------|----------|--------------|--------|
| 间联扫码 | 拉卡拉 | 是 | 是 | 三方 | 间清 | 是 | |
| 间联扫码 | 富友 | 是 | 否 | 三方 | 间清 | 是 | |
| 华夏银行 | 华夏 | 否 | 否 | 银行 | 直清 | 是 | |
| 华夏银行高费率 | 华夏 | 否 | 否 | 银行 | 直清 | 否 | |
| ...... | ...... | | | | | | |

#### 业务方配置表

| 业务方需求 | trade_app_id | 分账 | 场景 | ...... |
|------------|--------------|------|------|--------|
| 支付业务 | 1 | | | |
| 扫码点单 | | 需要（必须） | | |
| 多功能收银 | | 需要（必须） | | |
| 无忧收款 | | 不需要 | 线上（必须） | |
| 银行卡收款 | 1 | | | |
| ...... | | | | |

## 计算多业务交易参数

```mermaid
flowchart TD
    A[开始] --> B[获取业务方配置]
    B --> C{业务方是否需要分账?}

    C -->|是| D[筛选支持分账的收单机构]
    C -->|否| E[获取所有可用收单机构]

    D --> F{业务方是否有场景要求?}
    E --> F

    F -->|有| G[根据场景筛选参数<br/>如：线上场景]
    F -->|无| H[获取默认参数]

    G --> I{是否有指定配置?}
    H --> I

    I -->|有| J[应用指定产品配置]
    I -->|无| K[应用默认规则]

    J --> L{收单机构是否支持多费率?}
    K --> L

    L -->|是| M[可以配置多套费率]
    L -->|否| N[只能使用统一费率]

    M --> O[返回匹配的交易参数]
    N --> O
    O --> P[结束]

    style A fill:#e1f5fe
    style P fill:#e8f5e8
    style O fill:#fff3e0
```

## 计算多业务费率

```mermaid
flowchart TD
    A[开始] --> B[获取业务方和交易参数]
    B --> C{业务方forceCombo是否为true?}

    C -->|是| D[必须使用专有套餐<br/>如：线上业务、无忧收款]
    C -->|否| E{支付业务是否使用三方机构?}

    D --> F[配置专有费率套餐]

    E -->|是| G{多业务是否有特殊套餐配置?}
    E -->|否| H[多业务单独配置费率<br/>银行机构或直连]

    G -->|有| I[使用多业务特殊套餐]
    G -->|无| J[复用支付业务费率套餐]

    F --> K{是否需要参数费率绑定?}
    H --> K
    I --> K
    J --> K

    K -->|是| L[确保参数和费率匹配<br/>如：银行业务、支付源活动、线上业务]
    K -->|否| M[使用标准费率配置]

    L --> N{是否支持费率恢复?}
    M --> O[返回费率配置]

    N -->|是| P[保存原费率用于回切]
    N -->|否| Q[直接应用新费率]

    P --> O
    Q --> O
    O --> R[结束]

    style A fill:#e1f5fe
    style R fill:#e8f5e8
    style F fill:#ffebee
    style L fill:#fff3e0
```

## 业务流程分析

### 参数场景
1、新增商户入网
2、微信、支付宝商家认证
3、支付源活动报名
4、线上业务开通
5、手动切换

### 费率场景
1、新增商户入网
2、支付源活动报名
3、线上业务开通
4、产品费率活动申请
5、业务费率活动申请
