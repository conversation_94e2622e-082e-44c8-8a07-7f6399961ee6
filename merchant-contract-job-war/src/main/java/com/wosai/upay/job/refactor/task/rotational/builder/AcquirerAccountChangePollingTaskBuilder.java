package com.wosai.upay.job.refactor.task.rotational.builder;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.dto.BankAccountSimpleInfoBO;
import com.wosai.upay.job.refactor.task.rotational.AcquirerAccountChangePollingSubTaskProcessor;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalSubTaskTypeEnum;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalTaskContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 收单机构换卡轮询任务构建器
 * 
 * 用于创建收单机构换卡结果轮询任务的上下文参数
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Slf4j
public class AcquirerAccountChangePollingTaskBuilder {

    private String merchantSn;
    private Long contractTaskId;
    private Long contractSubTaskId;
    private String acquirer;
    private String acquirerMerchantId;
    private BankAccountSimpleInfoBO oldAccountInfo;
    private BankAccountSimpleInfoBO newAccountInfo;
    private String remark;
    private Boolean belongToContractTask;

    /**
     * 创建构建器实例
     */
    public static AcquirerAccountChangePollingTaskBuilder create() {
        return new AcquirerAccountChangePollingTaskBuilder();
    }

    /**
     * 设置商户号
     */
    public AcquirerAccountChangePollingTaskBuilder merchantSn(String merchantSn) {
        this.merchantSn = merchantSn;
        return this;
    }

    /**
     * 设置contract_task主键ID
     */
    public AcquirerAccountChangePollingTaskBuilder contractTaskId(Long contractTaskId) {
        this.contractTaskId = contractTaskId;
        return this;
    }

    /**
     * 设置contract_sub_task主键ID
     */
    public AcquirerAccountChangePollingTaskBuilder contractSubTaskId(Long contractSubTaskId) {
        this.contractSubTaskId = contractSubTaskId;
        return this;
    }

    /**
     * 设置是否属于contract任务
     */
    public AcquirerAccountChangePollingTaskBuilder belongToContractTask(Boolean belongToContractTask) {
        this.belongToContractTask = belongToContractTask;
        return this;
    }

    /**
     * 设置收单机构
     */
    public AcquirerAccountChangePollingTaskBuilder acquirer(String acquirer) {
        this.acquirer = acquirer;
        return this;
    }

    /**
     * 设置收单机构商户ID
     */
    public AcquirerAccountChangePollingTaskBuilder acquirerMerchantId(String acquirerMerchantId) {
        this.acquirerMerchantId = acquirerMerchantId;
        return this;
    }

    /**
     * 设置原账户信息
     */
    public AcquirerAccountChangePollingTaskBuilder oldAccountInfo(BankAccountSimpleInfoBO oldAccountInfo) {
        this.oldAccountInfo = oldAccountInfo;
        return this;
    }

    /**
     * 设置新账户信息
     */
    public AcquirerAccountChangePollingTaskBuilder newAccountInfo(BankAccountSimpleInfoBO newAccountInfo) {
        this.newAccountInfo = newAccountInfo;
        return this;
    }

    /**
     * 设置备注信息
     */
    public AcquirerAccountChangePollingTaskBuilder remark(String remark) {
        this.remark = remark;
        return this;
    }

    /**
     * 构建轮询任务上下文
     * 
     * @return 任务上下文JSON字符串
     */
    public String build() {
        RotationalTaskContext context = buildContext();
        String contextJson = JSON.toJSONString(context);
        
        log.info("Built account change polling task context, merchantSn={}, contractSubTaskId={}, acquirer={}, context={}", 
                merchantSn, contractSubTaskId, acquirer, contextJson);
        
        return contextJson;
    }

    /**
     * 构建任务上下文（返回对象）
     * 
     * @return 任务上下文对象
     */
    public RotationalTaskContext buildContext() {
        validate();
        
        RotationalTaskContext.Builder contextBuilder = RotationalTaskContext.builder()
                .merchantSn(merchantSn)
                .rotationId(String.valueOf(contractSubTaskId))
                .subTaskTypeEnum(RotationalSubTaskTypeEnum.ACQUIRER_ACCOUNT_CHANGE_POLLING)
                .belongToContractTask(belongToContractTask != null ? belongToContractTask : true);
        
        // 如果属于contract任务，设置相关ID
        if (Boolean.TRUE.equals(belongToContractTask)) {
            if (contractTaskId != null) {
                contextBuilder.contractTaskId(contractTaskId);
            }
            if (contractSubTaskId != null) {
                contextBuilder.contractSubTaskId(contractSubTaskId);
            }
        }
        
        // 添加业务参数
        addBusinessParams(contextBuilder);
        
        return contextBuilder.build();
    }

    /**
     * 添加业务参数到上下文构建器
     */
    private void addBusinessParams(RotationalTaskContext.Builder contextBuilder) {
        // 必要参数
        if (contractSubTaskId != null) {
            contextBuilder.addParam(AcquirerAccountChangePollingSubTaskProcessor.CONTRACT_SUB_TASK_ID_KEY, contractSubTaskId);
        }
        if (StringUtils.isNotBlank(acquirer)) {
            contextBuilder.addParam(AcquirerAccountChangePollingSubTaskProcessor.ACQUIRER_KEY, acquirer);
        }
        
        // 可选参数
        if (StringUtils.isNotBlank(acquirerMerchantId)) {
            contextBuilder.addParam(AcquirerAccountChangePollingSubTaskProcessor.ACQUIRER_MERCHANT_ID_KEY, acquirerMerchantId);
        }
        
        if (Objects.nonNull(oldAccountInfo)) {
            // 直接传递对象，避免二次序列化
            contextBuilder.addParam(AcquirerAccountChangePollingSubTaskProcessor.OLD_ACCOUNT_INFO_KEY, oldAccountInfo);
        }
        
        if (Objects.nonNull(newAccountInfo)) {
            // 直接传递对象，避免二次序列化
            contextBuilder.addParam(AcquirerAccountChangePollingSubTaskProcessor.NEW_ACCOUNT_INFO_KEY, newAccountInfo);
        }
        
        if (StringUtils.isNotBlank(remark)) {
            contextBuilder.addParam(AcquirerAccountChangePollingSubTaskProcessor.REMARK_KEY, remark);
        }
    }

    /**
     * 校验必要参数
     */
    private void validate() {
        if (StringUtils.isBlank(merchantSn)) {
            throw new IllegalArgumentException("merchantSn不能为空");
        }
        
        if (StringUtils.isBlank(acquirer)) {
            throw new IllegalArgumentException("acquirer不能为空");
        }
        
        // 如果设置为属于contract任务，相关ID不能为空
        if (Boolean.TRUE.equals(belongToContractTask)) {
            if (Objects.isNull(contractSubTaskId)) {
                throw new IllegalArgumentException("当belongToContractTask=true时，contractSubTaskId不能为空");
            }
        }
    }


    /**
     * 构建示例（用于测试和文档）
     */
    public static String buildExample() {
        // 示例旧账户信息
        BankAccountSimpleInfoBO oldAccount = new BankAccountSimpleInfoBO();
        oldAccount.setCardNumber("6217000010001234567");
        oldAccount.setHolder("张三");
        oldAccount.setType(2); // 对私

        // 示例新账户信息
        BankAccountSimpleInfoBO newAccount = new BankAccountSimpleInfoBO();
        newAccount.setCardNumber("6217000010007654321");
        newAccount.setHolder("张三");
        newAccount.setType(2); // 对私

        return AcquirerAccountChangePollingTaskBuilder.create()
                .merchantSn("TEST_MERCHANT_001")
                .contractTaskId(11111L)
                .contractSubTaskId(12345L)
                .acquirer("lklV3")
                .acquirerMerchantId("LKL_MERCHANT_001")
                .belongToContractTask(true)
                .oldAccountInfo(oldAccount)
                .newAccountInfo(newAccount)
                .remark("测试换卡")
                .build();
    }
}
