package com.wosai.upay.job.refactor.biz.params;

import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.model.http.LogParamsDto;
import com.shouqianba.cua.utils.object.StringExtensionUtils;
import com.shouqianba.model.dto.request.MerchantTradeParamsDisableReasonManageReqDTO;
import com.shouqianba.model.dto.response.CommonResultRspDTO;
import com.shouqianba.model.dto.response.DisableReasonRspDTO;
import com.shouqianba.model.enums.TradeParamsDisableReasonAccessSideEnum;
import com.shouqianba.model.enums.TradeParamsDisableReasonOperateTypeEnum;
import com.shouqianba.service.ContractRelatedMappingConfigService;
import com.shouqianba.service.MerchantProviderParamsService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.MerchantAppConfig;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.refactor.biz.provider.McProviderBiz;
import com.wosai.upay.job.refactor.dao.*;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.service.rpc.coreb.CoreBTradeConfigService;
import com.wosai.upay.job.refactor.service.rpc.coreb.req.CoreBTradeExtConfigRemoveRequest;
import com.wosai.upay.job.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商户交易参数处理
 *
 * <AUTHOR>
 * @date 2024/7/24 15:36
 */
@Component
@Slf4j
public class MerchantTradeParamsBiz {

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;


    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;


    @Resource
    private ContractRelatedMappingConfigService contractRelatedMappingConfigService;

    @Resource
    private McProviderDAO mcProviderDAO;

    @Resource
    private McAcquirerDAO mcAcquirerDAO;

    @Resource
    private LklV3ShopTermDAO lklV3ShopTermDAO;

    @Resource
    private ProviderTerminalDAO providerTerminalDAO;

    @Resource
    private ProviderTerminalBindConfigDAO providerTerminalBindConfigDAO;

    @Resource
    private McProviderBiz mcProviderBiz;

    @Resource
    protected TradeConfigService tradeConfigService;

    @Resource
    protected CoreBTradeConfigService coreBTradeConfigService;

    /**
     * 获取通道商户号
     * merchant_provider_params.provider_merchant_id
     *
     * @param merchantSn       商户号
     * @param acquirerTypeEnum 收单机构
     * @return 收单商户号
     */
    public Optional<String> getMerchantAcquirerMerchantId(String merchantSn, AcquirerTypeEnum acquirerTypeEnum) {
        String provider = contractRelatedMappingConfigService.getProviderByAcquirer(acquirerTypeEnum.getValue());
        if (StringUtils.isBlank(provider)) {
            return Optional.empty();
        }
        Optional<MerchantProviderParamsDO> paramsOpt = merchantProviderParamsDAO.getBySnAndProviderAndPayWay(merchantSn, Integer.valueOf(provider),
                PaywayEnum.ACQUIRER.getValue());
        return paramsOpt.map(MerchantProviderParamsDO::getProviderMerchantId);
    }

    /**
     * 根据商户号获取商户的交易参数
     *
     * @param merchantSn 商户号
     * @return 交易参数
     */
    public List<MerchantProviderParamsDO> listParamsByMerchantSn(String merchantSn) {
        return merchantProviderParamsDAO.listByMerchantSn(merchantSn);
    }

    /**
     * 根据商户号获取商户的交易参数
     * key: acquirer value: key: payWay value: value: MerchantProviderParamsDO
     *
     * @param merchantSn 商户号
     * @return 交易参数
     */
    public Map<String/*acquirer*/, Map<String/*payWay*/, List<MerchantProviderParamsDO>>> getMerchantAcquirerParamsMap(String merchantSn) {
        return listParamsByMerchantSn(merchantSn)
                .stream()
                .filter(t -> Objects.nonNull(t.getProvider()))
                .collect(Collectors.groupingBy(
                        t -> StringExtensionUtils.toSafeString(mcProviderBiz.getAcquirerByProvider(t.getProvider().toString()), ""),
                        Collectors.groupingBy(t -> StringExtensionUtils.toSafeString(t.getPayway()))));
    }

    /**
     * 根据id列表批量逻辑删除
     *
     * @param ids id列表
     */
    public void logicDeleteByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        merchantProviderParamsDAO.logicDeleteByIds(ids);
    }

    /**
     * 批量新增商户交易参数
     *
     * @param params 参数列表
     */
    public Integer batchInsertParams(List<MerchantProviderParamsDO> params) {
        if (CollectionUtils.isEmpty(params)) {
            return 0;
        }
        return merchantProviderParamsDAO.batchInsert(params);
    }


    /**
     * 根据商户号删除商户所有交易相关涉及的参数
     *
     * @param merchantSn 商户号
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteAllAcquirerRelatedParams(String merchantSn, String acquirer) {
        log.info("deleteAllAcquirerRelatedParams, merchantSn: {}, acquirer: {}", merchantSn, acquirer);
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
        if (Objects.isNull(mcAcquirerDO) || StringUtils.isBlank(mcAcquirerDO.getProvider())) {
            log.warn("deleteAllAcquirerRelatedParams, mcAcquirerDO is null or provider is blank, merchantSn: {}, acquirer: {}", merchantSn, acquirer);
            return;
        }
        Optional<MerchantProviderParamsDO> acquirerParamsOpt = merchantProviderParamsDAO
                .getMerchantProviderParamsByProviderAndPayway(merchantSn, Integer.valueOf(mcAcquirerDO.getProvider()), PaywayEnum.ACQUIRER.getValue());
        if (!acquirerParamsOpt.isPresent()) {
            log.warn("deleteAllAcquirerRelatedParams, acquirerParams is null, merchantSn: {}, acquirer: {}", merchantSn, acquirer);
            return;
        }
        List<String> providers = mcProviderDAO.listByAcquirer(acquirer).stream()
                .map(McProviderDO::getProvider).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        logicDeleteByMerchantSnAndProviders(merchantSn, providers);
        providerTerminalDAO.deleteByMerchantSnAndProviders(merchantSn, providers);
        providerTerminalBindConfigDAO.deleteByMerchantSnAndProviders(merchantSn, providers);
        if (StringUtils.equals(AcquirerTypeEnum.LKL_V3.getValue(), acquirer)) {
            lklV3ShopTermDAO.deleteByMerchantSn(merchantSn);
        }
        deleteAllTradeExtConfig(merchantSn, Integer.valueOf(mcAcquirerDO.getProvider()), acquirerParamsOpt.get().getProviderMerchantId());
    }

    /**
     * 删除交易侧所有的终端
     *
     * @param merchantSn         商户号
     * @param provider           收单机构provider
     * @param providerMerchantId 收单商户号
     */
    public void deleteAllTradeExtConfig(String merchantSn, Integer provider, String providerMerchantId) {
        CoreBTradeExtConfigRemoveRequest tradeExtConfigRemoveRequest = new CoreBTradeExtConfigRemoveRequest();
        tradeExtConfigRemoveRequest.setMerchantSn(merchantSn);
        tradeExtConfigRemoveRequest.setProvider(provider);
        tradeExtConfigRemoveRequest.setProviderMchId(providerMerchantId);
        tradeExtConfigRemoveRequest.setSnTypes(Lists.newArrayList(CoreBTradeExtConfigRemoveRequest.SN_TYPE_STORE,
                CoreBTradeExtConfigRemoveRequest.SN_TYPE_MERCHANT,
                CoreBTradeExtConfigRemoveRequest.SN_TYPE_TERMINAL,
                CoreBTradeExtConfigRemoveRequest.SN_TYPE_PROVIDER_MCH,
                CoreBTradeExtConfigRemoveRequest.SN_TYPE_STORE_SUB_MCH));
        try {
            coreBTradeConfigService.deleteTradeExtConfig(tradeExtConfigRemoveRequest);
        } catch (Exception e) {
            log.error("删除交易扩展配置失败, 商户:{},req:{}", merchantSn, tradeExtConfigRemoveRequest, e);
        }
    }

    /**
     * 根据商户号删除商户交易参数
     *
     * @param merchantSn 商户号
     */
    public void logicDeleteByMerchantSnAndProviders(String merchantSn, List<String> providers) {
        merchantProviderParamsDAO.logicDeleteBySnAndProviders(merchantSn, providers);
    }

    /**
     * 获取收单机构商户号
     *
     * @param merchantSn 商户号
     * @param acquirer 收单机构
     * @return 收单商户号
     */
    public Optional<String> getAcquirerMerchantId(String merchantSn, String acquirer) {
        if (StringUtils.isBlank(acquirer) || StringUtils.isBlank(merchantSn)) {
            return Optional.empty();
        }
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
        if (Objects.isNull(mcAcquirerDO) || StringUtils.isBlank(mcAcquirerDO.getProvider())) {
            return Optional.empty();
        }
        return merchantProviderParamsDAO.getByMerchantSnAndProviderAndPayWay(merchantSn,
                Integer.valueOf(mcAcquirerDO.getProvider()), PaywayEnum.ACQUIRER.getValue())
                .stream()
                .map(MerchantProviderParamsDO::getPayMerchantId)
                .findFirst();
    }

    /**
     * 删除支付侧参数
     *
     * @param merchantId 商户id
     * @param payWay     支付源
     */
    public void deletePaySideMerchantConfigParams(String merchantId, Integer payWay, Integer provider) {
        try {
            Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
            if (MapUtils.isEmpty(merchantConfig)) {
                return;
            }
            Integer existedProvider = MapUtils.getInteger(merchantConfig, MerchantConfig.PROVIDER);
            if (Objects.isNull(existedProvider) || !Objects.equals(provider, existedProvider)) {
                log.warn("删除支付测参数,provider不一致, merchantId:{}, pawWay:{}, provider:{}", merchantId, payWay, provider);
                return;
            }
            Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName("provider_trade_params_key");
            String tradeParamsKey = MapUtils.getString(providerTradeParamsKey, MapUtils.getString(merchantConfig, MerchantConfig.PROVIDER));
            Map params = MapUtils.getMap(merchantConfig, MerchantConfig.PARAMS);
            params.remove(tradeParamsKey);
            tradeConfigService.updateMerchantConfig(CollectionUtil.hashMap(
                    MerchantConfig.PARAMS, params,
                    DaoConstants.ID, BeanUtil.getPropString(merchantConfig, DaoConstants.ID)));
        } catch (Exception e) {
            log.warn("删除交易侧参数失败, merchantId:{}, payWay:{}", merchantId, payWay, e);
        }
    }

    /**
     * 删除支付侧多业务参数
     *
     * @param merchantId 商户id
     * @param payWay     支付源
     */
    public void deletePaySideMerchantAppConfigParams(String merchantId, String traAppId, Integer payWay, Integer provider) {
        try {
            Map merchantAppConfig = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, payWay, traAppId);
            if (MapUtils.isEmpty(merchantAppConfig)) {
                return;
            }
            Integer existedProvider = MapUtils.getInteger(merchantAppConfig, MerchantConfig.PROVIDER);
            if (Objects.isNull(existedProvider) || !Objects.equals(provider, existedProvider)) {
                log.warn("删除支付测参数,provider不一致, merchantId:{}, pawWay:{}, provider:{}, traAppId:{}", merchantId, payWay, provider, traAppId);
                return;
            }
            deleteMerchantAppConfigParams(merchantAppConfig);
        } catch (Exception e) {
            log.warn("删除交易侧多业务参数失败, merchantId:{}, payWay:{}, traAppId:{}", merchantId, payWay, traAppId,e);
        }
    }

    /**
     * 删除支付侧多业务参数
     *
     * @param merchantId 商户id
     * @param payWay     支付源
     */
    public void deletePaySideMerchantAppConfigParams(String merchantId, Integer payWay, Integer provider) {
        try {
            List<Map<String, Object>> merchantAppConfig = tradeConfigService.getMerchantAppConfigByMerchantIdAndProvider(merchantId, provider);
            if (CollectionUtils.isEmpty(merchantAppConfig)) {
                return;
            }
            for (Map<String, Object> merchantAppConfigMap : merchantAppConfig) {
                Integer paramsPayWay = MapUtils.getInteger(merchantAppConfigMap, MerchantAppConfig.PAYWAY);
                if (!Objects.equals(paramsPayWay, payWay)) {
                    continue;
                }
                deleteMerchantAppConfigParams(merchantAppConfigMap);
            }
        } catch (Exception e) {
            log.warn("删除交易侧多业务参数失败, merchantId:{}, payWay:{}, provider:{}", merchantId, payWay, provider, e);
        }
    }

    private void deleteMerchantAppConfigParams(Map<String, Object> merchantAppConfigMap) {
        Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName("provider_trade_params_key");
        String tradeParamsKey = MapUtils.getString(providerTradeParamsKey, MapUtils.getString(merchantAppConfigMap, MerchantConfig.PROVIDER));
        Map params = MapUtils.getMap(merchantAppConfigMap, MerchantConfig.PARAMS);
        params.remove(tradeParamsKey);
        tradeConfigService.updateMerchantAppConfig(CollectionUtil.hashMap(
                MerchantConfig.PARAMS, params,
                DaoConstants.ID, BeanUtil.getPropString(merchantAppConfigMap, DaoConstants.ID)));
    }

    @Resource(type = MerchantProviderParamsService.class)
    private MerchantProviderParamsService accessMerchantProviderParamsService;

    /**
     * 获取收单机构商户号禁用原因
     *
     * @param merchantSn 商户号
     * @param acquirer   收单机构
     * @return 禁用原因列表
     */
    public List<DisableReasonRspDTO> listMerchantAcquirerParamDisableReasons(String merchantSn, String acquirer) {
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
        if (Objects.isNull(mcAcquirerDO) || StringUtils.isBlank(mcAcquirerDO.getProvider())) {
            return Collections.emptyList();
        }
        Optional<MerchantProviderParamsDO> paramsOpt = merchantProviderParamsDAO.getBySnAndProviderAndPayWay(merchantSn, Integer.valueOf(mcAcquirerDO.getProvider()), PaywayEnum.ACQUIRER.getValue());
        if (!paramsOpt.isPresent()) {
            return Collections.emptyList();
        }
        return listDisableReasonsByAcquirerMerchantId(merchantSn, paramsOpt.get().getPayMerchantId());
    }

    /**
     * 根据收单机构商户号获取禁用原因列表
     *
     * @param merchantSn 商户号
     * @param acquirerMerchantId 收单机构商户号
     * @return 禁用原因列表
     */
    public List<DisableReasonRspDTO> listDisableReasonsByAcquirerMerchantId(String merchantSn, String acquirerMerchantId) {
        try {
            return accessMerchantProviderParamsService.listMerchantAcquirerMerchantIdDisableReason(merchantSn, acquirerMerchantId);
        } catch (Exception e) {
            log.error("listDisableReasonsByAcquirerMerchantId error {}", acquirerMerchantId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取收单机构交易参数
     *
     * @param merchantSn 商户号
     * @param acquirer   收单机构
     * @return 收单机构交易参数
     */
    public Optional<MerchantProviderParamsDO> getAcquirerParams(String merchantSn, String acquirer) {
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
        if (Objects.isNull(mcAcquirerDO)) {
            return Optional.empty();
        }
        return merchantProviderParamsDAO.getBySnAndProviderAndPayWay(merchantSn, Integer.valueOf(mcAcquirerDO.getProvider()), PaywayEnum.ACQUIRER.getValue());
    }

    /**
     * 禁用收单机构商户号
     * 商户应该只有一个收单机构商户号（未删除）
     */
    public void disableMerchantAcquirerMerchantId(String merchantSn, String acquirer, MerchantTradeParamsDisableReasonManageReqDTO req) {
        Optional<MerchantProviderParamsDO> acquirerParams = getAcquirerParams(merchantSn, acquirer);
        if (!acquirerParams.isPresent()) {
            return;
        }
        disableAcquirerMerchantId(acquirerParams.get().getPayMerchantId(), req);
    }

    /**
     * 禁用收单机构商户号
     */
    public void disableAcquirerMerchantId(String acquirerMerchantId, MerchantTradeParamsDisableReasonManageReqDTO req) {
        LogParamsDto logParamsDto = new LogParamsDto();
        logParamsDto.setUserId("system");
        logParamsDto.setUserName("system");
        accessMerchantProviderParamsService.manageAcquirerMerchantIdDisableReason(acquirerMerchantId, req, logParamsDto);
    }

    /**
     * 收单机构商户状态变更，管理禁用原因
     *
     * @return 禁用结果
     */
    public CuaCommonResultDTO manageDisableWhenAcquirerStatusChange(String merchantSn, String acquirerMerchantId, boolean close) {
        log.info("acquirerStatusCloseDisable, merchantSn:{}, acquirerMerchantId:{}", merchantSn, acquirerMerchantId);
        MerchantTradeParamsDisableReasonManageReqDTO req = new MerchantTradeParamsDisableReasonManageReqDTO();
        req.setMerchantSn(merchantSn);
        req.setOperateType(close ? TradeParamsDisableReasonOperateTypeEnum.ADD : TradeParamsDisableReasonOperateTypeEnum.DELETE);
        req.setAccessSide(TradeParamsDisableReasonAccessSideEnum.CUA);
        req.setOperator("system");
        req.setDisableReason("收单机构商户状态关闭");
        LogParamsDto logParamsDto = new LogParamsDto();
        logParamsDto.setUserId("system");
        logParamsDto.setUserName("system");
        try {
            CommonResultRspDTO commonResultRspDTO = accessMerchantProviderParamsService.manageAcquirerMerchantIdDisableReason(acquirerMerchantId, req, logParamsDto);
            return commonResultRspDTO.isSuccess() ? CuaCommonResultDTO.success() : CuaCommonResultDTO.fail(commonResultRspDTO.getMessage());
        } catch (Exception e) {
            log.error("acquirerStatusCloseDisable error , acquirerMerchantId:{}", acquirerMerchantId, e);
            return CuaCommonResultDTO.fail(e.getMessage());
        }
    }

    /**
     * 获取最近一次删除的收单机构商户号
     *
     * @param merchantSn 商户号
     * @param acquirer   收单机构
     */
    public Optional<MerchantProviderParamsDO> getLastedDeletedAcquirerMerchantId(String merchantSn, String acquirer) {
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
        if (Objects.isNull(mcAcquirerDO)) {
            return Optional.empty();
        }
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = merchantProviderParamsDAO.listDeletedParamsBySn(merchantSn);
        return merchantProviderParamsDOS.stream().filter(t -> Objects.equals(t.getProvider(), Integer.valueOf(mcAcquirerDO.getProvider())))
                .max(Comparator.comparingLong(MerchantProviderParamsDO::getMtime));
    }

    /**
     * 获取交易参数extra字段存的禁用原因
     *
     * @param extra extra字段
     * @return 禁用原因
     */
    public List<Object> getDisableReasons(byte[] extra) {
        try {
            Map extraMap = CommonUtil.bytes2Map(extra);
            if (extraMap.containsKey(MerchantProviderParamsDO.DISABLE_REASONS_KEY)) {
                return (List<Object>) extraMap.get(MerchantProviderParamsDO.DISABLE_REASONS_KEY);
            }
        } catch (Exception e) {
            log.warn("extra字段解析禁用原因失败", e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取饭卡支付参数
     *
     * @param merchantSn 商户号
     * @return 饭卡支付参数
     */
    public Optional<MerchantProviderParams> getFoodCardParamsByProvider(String merchantSn,Integer provider) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(provider)
                .andPaywayEqualTo(PaywayEnum.FOOD_CARD.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            return Optional.empty();
        }
        return merchantProviderParams.stream().findFirst();
    }
}
