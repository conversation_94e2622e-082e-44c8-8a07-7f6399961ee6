package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.refactor.mapper.AcquirerAccountChangeRecordMapper;
import com.wosai.upay.job.refactor.mapper.AcquirerSupportSettlementMapper;
import com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO;
import com.wosai.upay.job.refactor.model.enums.AcquirerAccountChangeStatusEnum;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 收单机构账户变更记录DAO
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Repository
@Slf4j
public class AcquirerAccountChangeRecordDAO extends AbstractBaseDAO<AcquirerAccountChangeRecordDO, AcquirerAccountChangeRecordMapper> {

    public AcquirerAccountChangeRecordDAO(SqlSessionFactory sqlSessionFactory, AcquirerAccountChangeRecordMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 查询指定商户在指定收单机构的成功变更记录（指定时间范围内）
     */
    public List<AcquirerAccountChangeRecordDO> findSuccessRecords(String merchantSn, String acquirer, 
                                                                 Date startDate, Date endDate) {
        if (StringUtils.isEmpty(merchantSn) || StringUtils.isEmpty(acquirer)) {
            throw new ContractBizException("merchantSn or acquirer is null");
        }
        
        LambdaQueryWrapper<AcquirerAccountChangeRecordDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AcquirerAccountChangeRecordDO::getMerchantSn, merchantSn)
               .eq(AcquirerAccountChangeRecordDO::getAcquirer, acquirer)
               .eq(AcquirerAccountChangeRecordDO::getStatus, AcquirerAccountChangeStatusEnum.SUCCESS.getValue())
               .ge(AcquirerAccountChangeRecordDO::getCompleteTime, startDate)
               .le(AcquirerAccountChangeRecordDO::getCompleteTime, endDate);
        
        return entityMapper.selectList(wrapper);
    }

    /**
     * 查询指定商户在指定收单机构、指定机构商户号的成功变更记录（指定时间范围内）
     */
    public List<AcquirerAccountChangeRecordDO> findSuccessRecords(String merchantSn, String acquirer,
                                                                  String acquirerMerchantId, Date startDate, Date endDate) {
        if (StringUtils.isBlank(merchantSn) || StringUtils.isBlank(acquirer)) {
            throw new ContractBizException("merchantSn or acquirer is null");
        }

        LambdaQueryWrapper<AcquirerAccountChangeRecordDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AcquirerAccountChangeRecordDO::getMerchantSn, merchantSn)
               .eq(AcquirerAccountChangeRecordDO::getAcquirer, acquirer)
               .eq(AcquirerAccountChangeRecordDO::getStatus, AcquirerAccountChangeStatusEnum.SUCCESS.getValue())
               .ge(AcquirerAccountChangeRecordDO::getCompleteTime, startDate)
               .le(AcquirerAccountChangeRecordDO::getCompleteTime, endDate);

        if (!WosaiStringUtils.isEmpty(acquirerMerchantId)) {
            wrapper.eq(AcquirerAccountChangeRecordDO::getAcquirerMerchantId, acquirerMerchantId);
        }

        return entityMapper.selectList(wrapper);
    }
}