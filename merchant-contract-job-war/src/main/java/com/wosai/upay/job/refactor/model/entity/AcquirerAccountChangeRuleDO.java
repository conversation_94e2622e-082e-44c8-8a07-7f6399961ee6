package com.wosai.upay.job.refactor.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 收单机构换卡规则表
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@TableName("acquirer_account_change_rule")
@Accessors(chain = true)
public class AcquirerAccountChangeRuleDO {
    
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 收单机构
     */
    @TableField(value = "acquirer")
    private String acquirer;
    
    /**
     * 规则名称
     */
    @TableField(value = "rule_name")
    private String ruleName;
    
    /**
     * 统计周期天数
     */
    @TableField(value = "period_days")
    private Integer periodDays;
    
    /**
     * 最大允许次数
     */
    @TableField(value = "max_count")
    private Integer maxCount;
    
    /**
     * 规则逻辑(JSON格式，支持复杂规则)
     */
    @TableField(value = "rule_logic")
    private String ruleLogic;
    
    /**
     * 创建时间
     */
    @TableField(value = "ctime")
    private Date ctime;
    
    /**
     * 更新时间
     */
    @TableField(value = "mtime")
    private Date mtime;
}