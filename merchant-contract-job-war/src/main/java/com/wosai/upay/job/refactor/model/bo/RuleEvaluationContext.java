package com.wosai.upay.job.refactor.model.bo;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.dto.BankAccountSimpleInfoBO;
import com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;

/**
 * 规则评估上下文
 * 用于缓存反序列化的账户信息，避免重复解析JSON
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Slf4j
public class RuleEvaluationContext {

    private final AcquirerAccountChangeRecordDO record;
    private BankAccountSimpleInfoBO oldAccountInfo;
    private BankAccountSimpleInfoBO newAccountInfo;

    public RuleEvaluationContext(AcquirerAccountChangeRecordDO record) {
        this.record = record;
    }

    /**
     * 获取老账户信息（懒加载，只解析一次）
     */
    public BankAccountSimpleInfoBO getOldAccountInfo() {
        if (oldAccountInfo == null && StringUtils.hasText(record.getOldAccountInfo())) {
            try {
                oldAccountInfo = JSON.parseObject(record.getOldAccountInfo(), BankAccountSimpleInfoBO.class);
            } catch (Exception e) {
                log.error("Parse old account info error, json: {}", record.getOldAccountInfo(), e);
                oldAccountInfo = new BankAccountSimpleInfoBO();
            }
        }
        return oldAccountInfo;
    }

    /**
     * 获取新账户信息（懒加载，只解析一次）
     */
    public BankAccountSimpleInfoBO getNewAccountInfo() {
        if (newAccountInfo == null && StringUtils.hasText(record.getNewAccountInfo())) {
            try {
                newAccountInfo = JSON.parseObject(record.getNewAccountInfo(), BankAccountSimpleInfoBO.class);
            } catch (Exception e) {
                log.error("Parse new account info error, json: {}", record.getNewAccountInfo(), e);
                newAccountInfo = new BankAccountSimpleInfoBO();
            }
        }
        return newAccountInfo;
    }

    /**
     * 从字段路径中提取值
     */
    public Object extractFieldValue(String field) {
        if (field.startsWith("old_account_info.")) {
            String subField = field.substring("old_account_info.".length());
            return extractFieldFromAccountInfo(getOldAccountInfo(), subField);
        } else if (field.startsWith("new_account_info.")) {
            String subField = field.substring("new_account_info.".length());
            return extractFieldFromAccountInfo(getNewAccountInfo(), subField);
        } else {
            return extractFieldFromRecord(field);
        }
    }

    /**
     * 从账户信息对象中提取字段值
     */
    private Object extractFieldFromAccountInfo(BankAccountSimpleInfoBO accountInfo, String fieldName) {
        if (accountInfo == null) {
            return null;
        }
        
        try {
            Field field = BankAccountSimpleInfoBO.class.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(accountInfo);
        } catch (Exception e) {
            log.error("Extract field from account info error, fieldName: {}", fieldName, e);
            return null;
        }
    }

    /**
     * 从记录对象中提取字段值
     */
    private Object extractFieldFromRecord(String fieldName) {
        try {
            Field field = record.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(record);
        } catch (Exception e) {
            log.error("Extract field from record error, fieldName: {}", fieldName, e);
            return null;
        }
    }
}