package com.wosai.upay.job.refactor.service.impl;

import com.alibaba.fastjson.*;
import com.google.common.base.Splitter;
import com.google.common.collect.*;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.MerchantTypeEnum;
import com.shouqianba.cua.enums.core.SettlementAccountTypeEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.shouqianba.cua.enums.type.DataOperationTypeEnum;
import com.shouqianba.cua.utils.object.*;
import com.shouqianba.cua.utils.page.LogicPageUtils;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.model.Industry;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.bank.info.api.service.IndustryV2Service;
import com.wosai.upay.job.model.dto.ObjectPropertyDTO;
import com.wosai.upay.job.model.dto.request.*;
import com.wosai.upay.job.model.dto.response.*;
import com.wosai.upay.job.refactor.biz.rule.GroupCombinedStrategyBiz;
import com.wosai.upay.job.refactor.config.redisson.DistributedLock;
import com.wosai.upay.job.refactor.dao.GroupRouteRuleDetailDAO;
import com.wosai.upay.job.refactor.dao.GroupRouteRulesDecisionDAO;
import com.wosai.upay.job.refactor.dao.McRuleGroupDAO;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.enume.LegalPersonTypeEnum;
import com.wosai.upay.job.refactor.model.enums.McObjectPropertyTypeEnum;
import com.wosai.upay.job.refactor.model.enums.PersonalCertificateTypeEnum;
import com.wosai.upay.job.refactor.model.enums.UseStatusEnum;
import com.wosai.upay.job.refactor.utils.MapUtils;
import com.wosai.upay.job.refactor.utils.ThreadPoolWorker;
import com.wosai.upay.job.service.GroupRouteRulesDecisionService;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.shouqianba.cua.model.page.NormalPagingResult;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 收单组路由规则决策服务
 * todo decision表新增禁用字段。移除有效状态字段
 *
 * <AUTHOR>
 * @date 2024/3/7 17:05
 */
@Service
@AutoJsonRpcServiceImpl
public class GroupRouteRulesDecisionServiceImpl implements GroupRouteRulesDecisionService {

    @Autowired
    public GroupRouteRulesDecisionServiceImpl(
            GroupRouteRulesDecisionDAO groupRouteRulesDecisionDAO,
            GroupRouteRuleDetailDAO groupRouteRulesDetailDAO,
            RedissonClient redissonClient,
            McRuleGroupDAO mcRuleGroupDAO,
            GroupCombinedStrategyBiz groupCombinedStrategyBiz,
            IndustryV2Service industryV2Service,
            DistrictsServiceV2 districtsServiceV2,
            OrganizationService organizationService) {
        this.groupRouteRulesDecisionDAO = groupRouteRulesDecisionDAO;
        this.groupRouteRulesDetailDAO = groupRouteRulesDetailDAO;
        this.redissonClient = redissonClient;
        this.mcRuleGroupDAO = mcRuleGroupDAO;
        this.groupCombinedStrategyBiz = groupCombinedStrategyBiz;
        this.industryV2Service = industryV2Service;
        this.districtsServiceV2 = districtsServiceV2;
        this.organizationService = organizationService;
        initObjectPropertyTypeConsumerMap();
    }

    private final RedissonClient redissonClient;

    private static final String CURRENT_DECISION_ID = "merchant-contract-job:groupRouteRulesDecisionId";

    private final GroupRouteRulesDecisionDAO groupRouteRulesDecisionDAO;

    private final GroupRouteRuleDetailDAO groupRouteRulesDetailDAO;

    private final McRuleGroupDAO mcRuleGroupDAO;

    private final GroupCombinedStrategyBiz groupCombinedStrategyBiz;

    private final IndustryV2Service industryV2Service;

    private final DistrictsServiceV2 districtsServiceV2;

    private final OrganizationService organizationService;

    private final EnumMap<McObjectPropertyTypeEnum, Function<List<GroupRouteRuleDetailRspDTO>, Map<String, String>>> objectPropertyTypeFunctionMap = Maps.newEnumMap(McObjectPropertyTypeEnum.class);


    private void initObjectPropertyTypeConsumerMap() {
        objectPropertyTypeFunctionMap.put(McObjectPropertyTypeEnum.MERCHANT_TYPE, groupRouteRuleDetailRspDTOS ->
                Arrays.stream(MerchantTypeEnum.values()).collect(Collectors.toMap(t -> t.getValue().toString(), MerchantTypeEnum::getText)));
        objectPropertyTypeFunctionMap.put(McObjectPropertyTypeEnum.BANK_ACCOUNT_TYPE, groupRouteRuleDetailRspDTOS ->
                Arrays.stream(BankAccountTypeEnum.values()).collect(Collectors.toMap(t -> t.getValue().toString(), BankAccountTypeEnum::getText)));
        objectPropertyTypeFunctionMap.put(McObjectPropertyTypeEnum.LEGAL_PERSON_TYPE, groupRouteRuleDetailRspDTOS ->
                Arrays.stream(LegalPersonTypeEnum.values()).collect(Collectors.toMap(t -> t.getValue().toString(), LegalPersonTypeEnum::getText)));
        objectPropertyTypeFunctionMap.put(McObjectPropertyTypeEnum.SETTLEMENT_ACCOUNT_TYPE, groupRouteRuleDetailRspDTOS ->
                Arrays.stream(SettlementAccountTypeEnum.values()).collect(Collectors.toMap(t -> t.getValue().toString(), SettlementAccountTypeEnum::getText)));
        objectPropertyTypeFunctionMap.put(McObjectPropertyTypeEnum.PERSONAL_CERTIFICATE_TYPE, groupRouteRuleDetailRspDTOS ->
                Arrays.stream(PersonalCertificateTypeEnum.values()).collect(Collectors.toMap(t -> t.getValue().toString(), PersonalCertificateTypeEnum::getText)));
        objectPropertyTypeFunctionMap.put(McObjectPropertyTypeEnum.PROVINCE_CODE, this::getProvinceCodeToNameMap);
        objectPropertyTypeFunctionMap.put(McObjectPropertyTypeEnum.CITY_CODE, this::getCityCodeToNameMap);
        objectPropertyTypeFunctionMap.put(McObjectPropertyTypeEnum.DISTRICT_CODE, this::getDistrictCodeToNameMap);
        objectPropertyTypeFunctionMap.put(McObjectPropertyTypeEnum.INDUSTRY, this::getIndustryCodeToNameMap);
        objectPropertyTypeFunctionMap.put(McObjectPropertyTypeEnum.PROMOTION_ORGANIZATION_PATH, this::getPromotionCodeToNameMap);
        objectPropertyTypeFunctionMap.put(McObjectPropertyTypeEnum.ORGANIZATION_PATH, this::getPromotionCodeToNameMap);
    }

    private Map<String, String> getPromotionCodeToNameMap(List<GroupRouteRuleDetailRspDTO> detailRspDTOList) {
        Set<String> pathSet = detailRspDTOList.stream().map(t -> parseObjectPropertyValue(t.getObjectPropertyValue())).flatMap(List::stream).collect(Collectors.toSet());
        HashMap<String, String> resMap = Maps.newHashMap();
        for (String path : pathSet) {
            // path的格式:323,3424,243,432 提取最后一个数字 即code
            if (StringUtils.isBlank(path)) {
                continue;
            }
            List<String> pathCodeList = Splitter.on(',').trimResults().omitEmptyStrings().splitToList(path);
            String pathCode = pathCodeList.get(pathCodeList.size() - 1);
            Map<String, Map> organizationMap = organizationService.getOrganizationByCode(Lists.newArrayList(pathCode));
            if (org.apache.commons.collections4.MapUtils.isNotEmpty(organizationMap) && organizationMap.containsKey(pathCode)) {
                resMap.put(path, org.apache.commons.collections4.MapUtils.getString(organizationMap.get(pathCode), "name_path"));
            }
        }
        return resMap;
    }

    private Map<String, String> getIndustryCodeToNameMap(List<GroupRouteRuleDetailRspDTO> detailRspDTOList) {
        return industryV2Service.allIndustry(null).stream().collect(Collectors.toMap(Industry::getId, t -> String.format("%s-%s", t.getCode1(), t.getCode2())));
    }

    private Map<String, String> getDistrictCodeToNameMap(List<GroupRouteRuleDetailRspDTO> detailRspDTOList) {
        Map<String, Map<String, Object>> allDistrictMap = getAllDistrictMap();
        Set<String> districtCodes = detailRspDTOList.stream().map(t -> parseObjectPropertyValue(t.getObjectPropertyValue())).flatMap(List::stream).collect(Collectors.toSet());
        HashMap<String, String> codeToNameMap = Maps.newHashMap();
        for (String districtCode : districtCodes) {
            if (allDistrictMap.containsKey(districtCode)) {
                Map<String, Object> district = allDistrictMap.get(districtCode);
                String provinceCode = org.apache.commons.collections4.MapUtils.getString(district, "province_code");
                String cityCode = org.apache.commons.collections4.MapUtils.getString(district, "city_code");
                codeToNameMap.put(districtCode, String.format("%s-%s-%s", provinceCode, cityCode, districtCode));
            }
        }
        return codeToNameMap;
    }

    private Map<String, Map<String, Object>> getAllDistrictMap() {
        Map<String, Map<String, Object>> allDistrictMap = (Map<String, Map<String, Object>>) districtsServiceV2.getAllDistricts(null);
        return allDistrictMap;
    }

    private Map<String, String> getCityCodeToNameMap(List<GroupRouteRuleDetailRspDTO> detailRspDTOList) {
        Map<String, Map<String, Object>> allDistrictMap = getAllDistrictMap();
        Set<String> districtCodes = detailRspDTOList.stream().map(t -> parseObjectPropertyValue(t.getObjectPropertyValue())).flatMap(List::stream).collect(Collectors.toSet());
        HashMap<String, String> codeToNameMap = Maps.newHashMap();
        for (String districtCode : districtCodes) {
            if (allDistrictMap.containsKey(districtCode)) {
                Map<String, Object> district = allDistrictMap.get(districtCode);
                String provinceCode = org.apache.commons.collections4.MapUtils.getString(district, "province_code");
                String cityCode = org.apache.commons.collections4.MapUtils.getString(district, "city_code");
                codeToNameMap.put(cityCode, String.format("%s-%s", provinceCode, cityCode));
            }
        }
        return codeToNameMap;
    }


    private Map<String, String> getProvinceCodeToNameMap(List<GroupRouteRuleDetailRspDTO> groupRouteRuleDetailRspDTOS) {
        return  districtsServiceV2.getAllProvince(null).stream().collect(Collectors.toMap(District::getCode, District::getName));
    }

    private List<GroupRouteRulesDecisionDO> listAllMcRulesDecisions() {
        return groupRouteRulesDecisionDAO.listAll();
    }

    private List<GroupRouteRuleDetailDO> listAllValidRuleDetails() {
        return groupRouteRulesDetailDAO.listAllValid();
    }

    /**
     * 根据筛选条件分页获取进件路由规则决策及detail,树形结构
     *
     * @param filterReqDTO 进件路由规则过滤条件
     * @return 节点进件路由规则决策
     */
    @Override
    public NormalPagingResult<GroupRouteRulesDecisionRspDTO> pageGroupRouteRules(GroupRouteRuleFilterReqDTO filterReqDTO) {
        List<GroupRouteRulesDecisionRspDTO> ruleList = listAllTopNodeGroupRouteRules();
        List<GroupRouteRulesDecisionRspDTO> filterDecisions = ruleList.stream()
                .filter(decisionRspDTO -> isMatchingChooseType(filterReqDTO, decisionRspDTO)
                        && isMatchingClassification(filterReqDTO, decisionRspDTO)
                        && isMatchingName(filterReqDTO, decisionRspDTO)
                        && isMatchingValidStatus(filterReqDTO, decisionRspDTO)
                        && isMatchingTimeRange(decisionRspDTO.getUpdateTime(), filterReqDTO.getUpdateStartDate(), filterReqDTO.getUpdateEndDate())
                        && isMatchingTimeRange(decisionRspDTO.getCreateTime(), filterReqDTO.getCreateStartDate(), filterReqDTO.getCreateEndDate())
                ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterReqDTO.getMerchantObjectPropertyDTOList())) {
            return LogicPageUtils.pageWithPagingResult(filterReqDTO.getPageNum(), filterReqDTO.getPageSize(),filterDecisions);
        }
        Map<Long/*decisionId*/, List<GroupRouteRuleDetailRspDTO>> detailListMap = Maps.newHashMapWithExpectedSize(ruleList.size());
        filterDecisions.forEach(decisionRspDTO -> {
            List<GroupRouteRuleDetailRspDTO> detailRspDTOS = recurveListGroupRuleDetailRspDTOs(decisionRspDTO);
            detailListMap.put(decisionRspDTO.getId(), detailRspDTOS);
        });
        Set<Long> decisionIdsWithFilterDetail = Sets.newHashSet();
        // 数据量很小(不超过几百条),不会有性能问题
        decisionIdLoop: for (Map.Entry<Long, List<GroupRouteRuleDetailRspDTO>> decisionIdDetailListEntry : detailListMap.entrySet()) {
            Long decisionId = decisionIdDetailListEntry.getKey();
            List<GroupRouteRuleDetailRspDTO> detailList = decisionIdDetailListEntry.getValue();
            for (MerchantObjectPropertyDTO propertyDTO : filterReqDTO.getMerchantObjectPropertyDTOList()) {
                boolean isSingleFilterPropertyMatch = false;
                for (GroupRouteRuleDetailRspDTO groupRouteRuleDetailRspDTO : detailList) {
                    if (StringUtils.equals(propertyDTO.getObjectPropertyType(), groupRouteRuleDetailRspDTO.getObjectPropertyType())
                            && StringUtils.equals(propertyDTO.getLogicalOperationType(), groupRouteRuleDetailRspDTO.getLogicalOperationType())
                            && CollectionUtils.containsAny(propertyDTO.getObjectPropertyValue(),
                            groupRouteRuleDetailRspDTO.getObjectPropertyDTOList().stream().map(ObjectPropertyDTO::getObjectPropertyValue).collect(Collectors.toSet()))) {
                        isSingleFilterPropertyMatch = true;
                        break;
                    }
                }
                if (!isSingleFilterPropertyMatch) {
                    continue decisionIdLoop;
                }
            }
            decisionIdsWithFilterDetail.add(decisionId);
        }
        return LogicPageUtils.pageWithPagingResult(filterReqDTO.getPageNum(), filterReqDTO.getPageSize(),
                filterDecisions.stream().filter(t -> decisionIdsWithFilterDetail.contains(t.getId())).collect(Collectors.toList()));
    }

    private List<GroupRouteRuleDetailRspDTO> recurveListGroupRuleDetailRspDTOs(GroupRouteRulesDecisionRspDTO decisionRspDTO) {
        List<GroupRouteRuleDetailRspDTO> detailList = Lists.newArrayList();
        if (Objects.nonNull(decisionRspDTO)) {
            if (CollectionUtils.isNotEmpty(decisionRspDTO.getGroupRouteRuleDetailRspDTOList())) {
                detailList.addAll(decisionRspDTO.getGroupRouteRuleDetailRspDTOList());
            }
            if (CollectionUtils.isNotEmpty(decisionRspDTO.getChildrenRulesDecisionNodeList())) {
                for (GroupRouteRulesDecisionRspDTO child : decisionRspDTO.getChildrenRulesDecisionNodeList()) {
                    detailList.addAll(recurveListGroupRuleDetailRspDTOs(child));
                }
            }
        }
        return detailList;
    }



    private boolean isMatchingChooseType(GroupRouteRuleFilterReqDTO filterReqDTO, GroupRouteRulesDecisionRspDTO decisionRspDTO) {
        return StringUtils.isBlank(filterReqDTO.getChooseType()) ||
                Objects.equals(filterReqDTO.getChooseType(), decisionRspDTO.getChooseType());
    }

    private boolean isMatchingClassification(GroupRouteRuleFilterReqDTO filterReqDTO, GroupRouteRulesDecisionRspDTO decisionRspDTO) {
        return StringUtils.isBlank(filterReqDTO.getClassification()) ||
                StringUtils.containsIgnoreCase(decisionRspDTO.getClassification(), filterReqDTO.getClassification());
    }

    private boolean isMatchingName(GroupRouteRuleFilterReqDTO filterReqDTO, GroupRouteRulesDecisionRspDTO decisionRspDTO) {
        return StringUtils.isBlank(filterReqDTO.getName()) ||
                StringUtils.containsIgnoreCase(decisionRspDTO.getName(), filterReqDTO.getName());
    }

    private boolean isMatchingValidStatus(GroupRouteRuleFilterReqDTO filterReqDTO, GroupRouteRulesDecisionRspDTO decisionRspDTO) {
        return filterReqDTO.getValidStatus() == null ||
                Objects.equals(filterReqDTO.getValidStatus(), decisionRspDTO.getValidStatus());
    }


    private boolean isMatchingTimeRange(Timestamp entityTime, Timestamp filterStartTime, Timestamp filterEndTime) {
        if (Objects.isNull(entityTime)) {
            return false;
        }
        LocalDate entityDate = entityTime.toLocalDateTime().toLocalDate();
        LocalDate filterStartDate = Objects.nonNull(filterStartTime) ? filterStartTime.toLocalDateTime().toLocalDate() : null;
        LocalDate filterEndDate = Objects.nonNull(filterEndTime) ? filterEndTime.toLocalDateTime().toLocalDate() : null;
        if (Objects.isNull(filterStartDate) && Objects.isNull(filterEndDate)) {
            return true;
        }
        if (Objects.nonNull(filterStartDate) && entityDate.isBefore(filterStartDate)) {
            return false;
        }
        if (Objects.nonNull(filterEndDate) && entityDate.isAfter(filterEndDate)) {
            return false;
        }
        return true;
    }

    /**
     * 获取所有的顶层节点进件路由规则决策及detail,树形结构
     * 规则数据量很小,没有性能问题
     * 因为是树状结构数据,所以不适合多次io查询,需要一次性查询出来
     *
     * @return 顶层节点进件路由规则决策及detail
     */
    @Override
    public List<GroupRouteRulesDecisionRspDTO> listAllTopNodeGroupRouteRules() {
        List<GroupRouteRulesDecisionRspDTO> groupRouteRulesDecisionRspDTOS = listAllGroupRouteRules().stream()
                .filter(t -> Objects.equals(t.getParentId(), 0L))
                .sorted(Comparator.comparing(GroupRouteRulesDecisionRspDTO::getPriority))
                .collect(Collectors.toList());
        Set<Long> groupStrategyIds = groupRouteRulesDecisionRspDTOS.stream().map(GroupRouteRulesDecisionRspDTO::getGroupStrategyId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        List<GroupCombinedStrategyDO> groupCombinedStrategyDOS = groupCombinedStrategyBiz.listCombinedStrategyByStrategyIds(groupStrategyIds);
        List<GroupCombinedStrategyDetailDO> strategyDetailDOS = groupCombinedStrategyBiz.listGroupDetailByStrategyIds(groupStrategyIds);
        Map<Long, GroupCombinedStrategyDO> groupCombinedStrategyDOMap = groupCombinedStrategyDOS.stream()
                .collect(Collectors.toMap(GroupCombinedStrategyDO::getId, Function.identity()));
        Map<Long, List<GroupCombinedStrategyDetailDO>> strategyDetailDOMap = strategyDetailDOS.stream()
                .collect(Collectors.groupingBy(GroupCombinedStrategyDetailDO::getGroupStrategyId));
        return groupRouteRulesDecisionRspDTOS.stream().peek(decisionRspDTO -> {
            Long groupStrategyId = decisionRspDTO.getGroupStrategyId();
            MapUtils.ifPresent(groupCombinedStrategyDOMap, groupStrategyId, groupCombinedStrategyDO ->
                decisionRspDTO.setGroupCombinedStrategyRspDTO(BeanCopyUtils.copyProperties(groupCombinedStrategyDO, GroupCombinedStrategyRspDTO.class))
            );
            MapUtils.ifPresent(strategyDetailDOMap, groupStrategyId, strategyDetailDOList ->
                decisionRspDTO.setGroupCombinedStrategyDetailRspDTOList(BeanCopyUtils.copyList(strategyDetailDOList, GroupCombinedStrategyDetailRspDTO.class))
            );
        }).collect(Collectors.toList());
    }

    @NotNull
    private List<GroupRouteRulesDecisionRspDTO> listAllGroupRouteRules() {
        List<GroupRouteRulesDecisionDO> decisionDOS = Lists.newArrayList();
        List<GroupRouteRuleDetailDO> detailDOS = Lists.newArrayList();
        ThreadPoolWorker.of()
                .addWork(() -> decisionDOS.addAll(listAllMcRulesDecisions()))
                .addWork(() -> detailDOS.addAll(listAllValidRuleDetails()))
                .doWorks();
        List<GroupRouteRulesDecisionRspDTO> groupRouteRulesDecisionRspDTOS = BeanCopyUtils.copyList(decisionDOS, GroupRouteRulesDecisionRspDTO.class);
        List<GroupRouteRuleDetailRspDTO> groupRouteRuleDetailRspDTOS = BeanCopyUtils.copyList(detailDOS, GroupRouteRuleDetailRspDTO.class);
        convertConcretePropertyValue(groupRouteRuleDetailRspDTOS);
        Map<Long, List<GroupRouteRulesDecisionRspDTO>> decisionMap = groupRouteRulesDecisionRspDTOS.stream()
                .collect(Collectors.groupingBy(GroupRouteRulesDecisionRspDTO::getParentId));
        Map<Long, List<GroupRouteRuleDetailRspDTO>> detailMap = groupRouteRuleDetailRspDTOS.stream()
                .collect(Collectors.groupingBy(GroupRouteRuleDetailRspDTO::getRuleDecisionId));
        groupRouteRulesDecisionRspDTOS.forEach(decisionRspDTO -> {
            decisionRspDTO.setGroupRouteRuleDetailRspDTOList(detailMap.getOrDefault(decisionRspDTO.getId(), Lists.newArrayList()));
            decisionRspDTO.setChildrenRulesDecisionNodeList(decisionMap.getOrDefault(decisionRspDTO.getId(), Lists.newArrayList()));
        });
        return groupRouteRulesDecisionRspDTOS;
    }


    private void convertConcretePropertyValue(List<GroupRouteRuleDetailRspDTO> groupRouteRuleDetailRspDTOS) {
        if (CollectionUtils.isEmpty(groupRouteRuleDetailRspDTOS)) {
            return;
        }
        groupRouteRuleDetailRspDTOS
                .stream()
                .collect(Collectors.groupingBy(GroupRouteRuleDetailRspDTO::getObjectPropertyType))
                .forEach((propertyType, detailRspDTOList)
                        -> EnumUtils.ofNullable(McObjectPropertyTypeEnum.class, propertyType)
                                .ifPresent(mcObjectPropertyTypeEnum -> doConvertConcretePropertyValue(mcObjectPropertyTypeEnum, detailRspDTOList))
                );
    }

    private void doConvertConcretePropertyValue(McObjectPropertyTypeEnum mcObjectPropertyTypeEnum, List<GroupRouteRuleDetailRspDTO> detailRspDTOList) {
        if (CollectionUtils.isEmpty(detailRspDTOList)) {
            return;
        }
        Map<String, String> valueToMeaningMap = objectPropertyTypeFunctionMap.getOrDefault(mcObjectPropertyTypeEnum, list -> Collections.emptyMap()).apply(detailRspDTOList);
        for (GroupRouteRuleDetailRspDTO groupRouteRuleDetailRspDTO : detailRspDTOList) {
            String originalValue = groupRouteRuleDetailRspDTO.getObjectPropertyValue();
            List<String> objectPropertyValueList = parseObjectPropertyValue(originalValue);
            List<ObjectPropertyDTO> objectPropertyDTOList = Lists.newArrayListWithCapacity(objectPropertyValueList.size());
            for (String objectPropertyValue : objectPropertyValueList) {
                ObjectPropertyDTO objectPropertyDTO = new ObjectPropertyDTO();
                objectPropertyDTO.setObjectPropertyValue(objectPropertyValue);
                objectPropertyDTO.setObjectPropertyMeaning(valueToMeaningMap.getOrDefault(objectPropertyValue, ""));
                objectPropertyDTOList.add(objectPropertyDTO);
            }
            groupRouteRuleDetailRspDTO.setObjectPropertyDTOList(objectPropertyDTOList);
        }
    }

    public List<String> parseObjectPropertyValue(String objectPropertyValue) {
        if (StringUtils.isBlank(objectPropertyValue)) {
            return Collections.emptyList();
        }
        JSONValidator validator = JSONValidator.from(objectPropertyValue);
        if (validator.validate()) {
            Object json = JSON.parse(objectPropertyValue);
            if (json instanceof JSONArray) {
                JSONArray jsonArray = (JSONArray) json;
                return jsonArray.toJavaList(String.class);
            }
        }
        return Collections.singletonList(objectPropertyValue);
    }

    /**
     * 根据id获取进件路由规则决策及detail,树形结构
     *
     * @param id 规则决策id
     * @return 进件路由规则决策及detail
     */
    @Override
    public GroupRouteRulesDecisionRspDTO getGroupRouteRulesDecisionById(Long id) {
        return listAllTopNodeGroupRouteRules().stream().filter(t -> Objects.equals(t.getId(), id)).findFirst().orElse(null);
    }

    /**
     * 根据id获取进件路由规则决策的详细信息
     *
     * @param id 规则决策id
     * @return 进件路由规则决策的详细信息
     */
    @Override
    public GroupRouteRuleDetailRspDTO getGroupRouteRuleDetailById(Long id) {
        Optional<GroupRouteRuleDetailDO> groupRouteRuleDetailDO = groupRouteRulesDetailDAO.getByPrimaryKey(id);
        return groupRouteRuleDetailDO.map(routeRuleDetailDO -> BeanCopyUtils.copyProperties(routeRuleDetailDO, GroupRouteRuleDetailRspDTO.class)).orElse(null);
    }

    /**
     * 根据id删除进件路由规则决策
     * 需要删除子节点以及节点对应的detail
     *
     * @param id 主键id
     * @return effect rows
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteGroupRouteRuleById(Long id) {
        Set<Long> childrenDecisionIds = Sets.newHashSet();
        GroupRouteRulesDecisionRspDTO decisionRspDTO = getGroupRouteRulesDecisionById(id);
        recurveCollectChildrenDecisionIds(id, childrenDecisionIds, Lists.newArrayList(decisionRspDTO));
        Integer deleteRows = groupRouteRulesDecisionDAO.batchDeleteByPrimaryKeys(childrenDecisionIds);
        groupRouteRulesDetailDAO.batchDeleteByRuleDecisionIds(childrenDecisionIds);
        return deleteRows;
    }

    private void recurveCollectChildrenDecisionIds(Long id, Set<Long> childrenDecisionIds, List<GroupRouteRulesDecisionRspDTO> decisionRspDTOS) {
        childrenDecisionIds.add(id);
        if (decisionRspDTOS.isEmpty()) {
            return;
        }
        for (GroupRouteRulesDecisionRspDTO child : decisionRspDTOS) {
            recurveCollectChildrenDecisionIds(child.getId(), childrenDecisionIds, child.getChildrenRulesDecisionNodeList());
        }
    }

    /**
     * 根据id删除进件路由规则决策细节
     *
     * @param id 主键id
     * @return effect rows
     */
    @Override
    public Integer deleteGroupRouteRuleDetailById(Long id) {
        return groupRouteRulesDetailDAO.deleteByPrimaryKey(id);
    }

    /**
     * 新增进件路由规则决策detail
     *
     * @param groupRouteRuleDetailReqDTO 进件路由规则决策detail
     * @return effect rows
     */
    @Override
    public Integer insertGroupRouteRuleDetail(GroupRouteRuleDetailRepDTO groupRouteRuleDetailReqDTO) {
        if (Objects.isNull(groupRouteRuleDetailReqDTO) || Objects.isNull(groupRouteRuleDetailReqDTO.getRuleDecisionId())) {
            return 0;
        }
        GroupRouteRuleDetailDO groupRouteRuleDetailDO = BeanCopyUtils.copyProperties(groupRouteRuleDetailReqDTO, GroupRouteRuleDetailDO.class);
        groupRouteRuleDetailDO.setObjectPropertyValue(JSON.toJSONString(groupRouteRuleDetailReqDTO.getObjectPropertyValue()));
        return groupRouteRulesDetailDAO.insertOne(groupRouteRuleDetailDO);
    }

    /**
     * 更新进件路由规则决策detail
     *
     * @param groupRouteRuleDetailReqDTO 进件路由规则决策detail
     * @return effect rows
     */
    @Override
    public Integer updateGroupRouteRuleDetail(GroupRouteRuleDetailRepDTO groupRouteRuleDetailReqDTO) {
        if (Objects.isNull(groupRouteRuleDetailReqDTO) || Objects.isNull(groupRouteRuleDetailReqDTO.getRuleDecisionId())) {
            return 0;
        }
        GroupRouteRuleDetailDO groupRouteRuleDetailDO = BeanCopyUtils.copyProperties(groupRouteRuleDetailReqDTO, GroupRouteRuleDetailDO.class);
        groupRouteRuleDetailDO.setObjectPropertyValue(JSON.toJSONString(groupRouteRuleDetailReqDTO.getObjectPropertyValue()));
        return groupRouteRulesDetailDAO.updateByPrimaryKeySelective(groupRouteRuleDetailDO);
    }


    /**
     * 新增进件路由规则决策
     *
     * @param groupRouteRulesDecisionReqDTO 进件路由规则决策
     * @return effect rows
     */
    @Override
    @DistributedLock(key = "merchant-contract-job:insertGroupRouteRule:lock", waitTime = 10L)
    @Transactional(rollbackFor = Exception.class)
    public Integer insertGroupRouteRule(GroupRouteRulesDecisionReqDTO groupRouteRulesDecisionReqDTO) {
        if (Objects.isNull(groupRouteRulesDecisionReqDTO)) {
            return 0;
        }
        verifyPriority(groupRouteRulesDecisionReqDTO, DataOperationTypeEnum.INSERT);
        redissonClient.getAtomicLong(CURRENT_DECISION_ID).set(groupRouteRulesDecisionDAO.getMaxId().orElse(0L));
        recurvePopulateDecisionParentIdAndDetailId(groupRouteRulesDecisionReqDTO, groupRouteRulesDecisionReqDTO.getParentId());
        List<GroupRouteRulesDecisionDO> groupRouteRulesDecisionDOS = listGroupRulesDecisionNodes(groupRouteRulesDecisionReqDTO);
        GroupCombinedStrategyDO groupCombinedStrategyDO = getGroupCombinedStrategyDO(groupRouteRulesDecisionReqDTO);
        groupRouteRulesDecisionDOS.stream().filter(t -> Objects.equals(t.getParentId(), 0L)).findFirst()
                .ifPresent(groupRouteRulesDecisionDO -> groupRouteRulesDecisionDO.setGroupStrategyId(groupCombinedStrategyDO.getId()));
        List<GroupRouteRuleDetailDO> groupRouteRuleDetailDOS = listGroupRuleDetailNodes(groupRouteRulesDecisionReqDTO);
        Integer effectRows = 0;
        supplementUpdateByAndCreateBy(groupRouteRulesDecisionReqDTO.getCreateBy(), groupRouteRulesDecisionReqDTO.getUpdateBy(),groupRouteRulesDecisionDOS, groupRouteRuleDetailDOS);
        effectRows += groupRouteRulesDecisionDAO.batchInsert(groupRouteRulesDecisionDOS);
        effectRows += groupRouteRulesDetailDAO.batchInsert(groupRouteRuleDetailDOS);
        return effectRows;
    }

    private void supplementUpdateByAndCreateBy(String createBy, String updateBy, List<GroupRouteRulesDecisionDO> groupRouteRulesDecisionDOS,
                                               List<GroupRouteRuleDetailDO> groupRouteRuleDetailDOS) {
        boolean createByNotBlank = StringUtils.isNotBlank(createBy);
        boolean updateByNotBlank = StringUtils.isNotBlank(updateBy);
        groupRouteRulesDecisionDOS.forEach(decisionDO -> {
            if (createByNotBlank) {
                decisionDO.setCreateBy(createBy);
            }
            if (updateByNotBlank) {
                decisionDO.setUpdateBy(updateBy);
            }
        });
        groupRouteRuleDetailDOS.forEach(detailDO -> {
            if (createByNotBlank) {
                detailDO.setCreateBy(createBy);
            }
            if (updateByNotBlank) {
                detailDO.setUpdateBy(updateBy);
            }
        });
    }

    private void verifyPriority(GroupRouteRulesDecisionReqDTO groupRouteRulesDecisionReqDTO, DataOperationTypeEnum operationTypeEnum) {
        if (Objects.isNull(groupRouteRulesDecisionReqDTO)
                || Objects.isNull(groupRouteRulesDecisionReqDTO.getPriority())
                || groupRouteRulesDecisionReqDTO.getPriority() < 0
                || groupRouteRulesDecisionReqDTO.getPriority() >= 65535) {
            throw new ContractBizException("优先级格式校验未通过,请输入0-65535之间的整数");
        }
        Long id = groupRouteRulesDecisionReqDTO.getId();
        Integer priority = groupRouteRulesDecisionReqDTO.getPriority();
        List<GroupRouteRulesDecisionDO> decisionDOList = groupRouteRulesDecisionDAO.listByPriority(priority);
        if (CollectionUtils.isEmpty(decisionDOList)) {
            return;
        }
        if (Objects.equals(operationTypeEnum, DataOperationTypeEnum.INSERT)) {
            throw new ContractBizException("优先级:" + priority + "已被占用");
        }
        if (decisionDOList.stream().anyMatch(t -> !Objects.equals(t.getId(), id))) {
            throw new ContractBizException("优先级:" + priority + "已被占用");
        }
    }

    /**
     * 更新进件路由规则决策
     * 更新之前会对已存在的规则,嵌套规则,规则详情先置为无效
     *
     * @param groupRouteRulesDecisionReqDTO 进件路由规则决策
     * @return effect rows
     */
    @Override
    @DistributedLock(key = "merchant-contract-job:updateGroupRouteRule:lock", waitTime = 10L)
    @Transactional(rollbackFor = Exception.class)
    public Integer updateGroupRouteRule(GroupRouteRulesDecisionReqDTO groupRouteRulesDecisionReqDTO) {
        if (Objects.isNull(groupRouteRulesDecisionReqDTO)) {
            return 0;
        }
        verifyPriority(groupRouteRulesDecisionReqDTO, DataOperationTypeEnum.UPDATE);
        // 重新构建树状结构之间的id关系,可能有新增的嵌套子规则和规则细节,生成对应的id
        recurvePopulateDecisionParentIdAndDetailId(groupRouteRulesDecisionReqDTO, groupRouteRulesDecisionReqDTO.getParentId());
        GroupRouteRulesDecisionRspDTO alreadyExistedDecision = getGroupRouteRulesDecisionById(groupRouteRulesDecisionReqDTO.getId());
        Set<Long> existedDecisionIds = Sets.newHashSet();
        recurveCollectChildrenDecisionIds(groupRouteRulesDecisionReqDTO.getId(), existedDecisionIds, Lists.newArrayList(alreadyExistedDecision));
        Set<Long> existedDetailIds = recurveListGroupRuleDetailRspDTOs(alreadyExistedDecision).stream().map(GroupRouteRuleDetailRspDTO::getId).collect(Collectors.toSet());
        groupRouteRulesDecisionDAO.updateValidStatusByIds(existedDecisionIds, ValidStatusEnum.INVALID.getValue());
        groupRouteRulesDetailDAO.updateValidStatusByIds(existedDetailIds, ValidStatusEnum.INVALID.getValue());
        GroupCombinedStrategyDO groupCombinedStrategyDO = getGroupCombinedStrategyDO(groupRouteRulesDecisionReqDTO);
        List<GroupRouteRulesDecisionDO> groupRouteRulesDecisionDOS = listGroupRulesDecisionNodes(groupRouteRulesDecisionReqDTO);
        groupRouteRulesDecisionDOS.stream().filter(t -> Objects.equals(t.getParentId(), 0L)).findFirst()
                .ifPresent(groupRouteRulesDecisionDO -> groupRouteRulesDecisionDO.setGroupStrategyId(groupCombinedStrategyDO.getId()));
        List<GroupRouteRuleDetailDO> groupRouteRuleDetailDOS = listGroupRuleDetailNodes(groupRouteRulesDecisionReqDTO);
        Integer effectRows = 0;
        supplementUpdateByAndCreateBy(null, groupRouteRulesDecisionReqDTO.getUpdateBy(), groupRouteRulesDecisionDOS, groupRouteRuleDetailDOS);
        effectRows += groupRouteRulesDecisionDAO.batchInsertOrUpdateById(groupRouteRulesDecisionDOS);
        effectRows += groupRouteRulesDetailDAO.batchInsertOrUpdateById(groupRouteRuleDetailDOS);
        return effectRows;
    }

    private GroupCombinedStrategyDO getGroupCombinedStrategyDO(GroupRouteRulesDecisionReqDTO groupRouteRulesDecisionReqDTO) {
        List<GroupCombinedStrategyRepDTO> groupCombinedStrategyRepDTOList = groupRouteRulesDecisionReqDTO.getGroupCombinedStrategyRepDTOList();
        Integer groupCombinedStrategyType = groupRouteRulesDecisionReqDTO.getGroupCombinedStrategyType();
        GroupCombinedStrategyDO groupCombinedStrategyDO = groupCombinedStrategyBiz.insertAndGetGroupCombineStrategy(groupCombinedStrategyType,
                BeanCopyUtils.copyList(groupCombinedStrategyRepDTOList, GroupCombinedStrategyDetailDO.class));
        return groupCombinedStrategyDO;
    }

    private void recurvePopulateDecisionParentIdAndDetailId(GroupRouteRulesDecisionReqDTO decisionNode, Long parentId) {
        if (Objects.isNull(decisionNode)) {
            return;
        }
        decisionNode.setParentId(Objects.isNull(parentId) ? 0L : parentId);
        if (Objects.isNull(decisionNode.getId())) {
            RAtomicLong decisionId = redissonClient.getAtomicLong(CURRENT_DECISION_ID);
            decisionId.incrementAndGet();
            decisionNode.setId(decisionId.get());
        }
        List<GroupRouteRuleDetailRepDTO> groupRouteRuleDetailRepDTOList = decisionNode.getGroupRouteRuleDetailRepDTOList();
        if (CollectionUtils.isNotEmpty(groupRouteRuleDetailRepDTOList)) {
            groupRouteRuleDetailRepDTOList.forEach(detail -> detail.setRuleDecisionId(decisionNode.getId()));
        }
        if (CollectionUtils.isNotEmpty(decisionNode.getChildrenRulesDecisionNodeList())) {
            for (GroupRouteRulesDecisionReqDTO child : decisionNode.getChildrenRulesDecisionNodeList()) {
                recurvePopulateDecisionParentIdAndDetailId(child, decisionNode.getId());
            }
        }
    }

    private List<GroupRouteRuleDetailDO> listGroupRuleDetailNodes(GroupRouteRulesDecisionReqDTO groupRouteRulesDecisionReqDTO) {
        return recurveListGroupRuleDetailRepDTOs(groupRouteRulesDecisionReqDTO)
                .stream()
                .map(groupRouteRuleDetailRepDTO -> {
                    GroupRouteRuleDetailDO groupRouteRuleDetailDO = BeanCopyUtils.copyProperties(groupRouteRuleDetailRepDTO, GroupRouteRuleDetailDO.class);
                    groupRouteRuleDetailDO.setObjectPropertyValue(JSON.toJSONString(groupRouteRuleDetailRepDTO.getObjectPropertyValue()));
                    return groupRouteRuleDetailDO;
                }).collect(Collectors.toList());
    }

    private List<GroupRouteRuleDetailRepDTO> recurveListGroupRuleDetailRepDTOs(GroupRouteRulesDecisionReqDTO decisionNode) {
        List<GroupRouteRuleDetailRepDTO> detailList = Lists.newArrayList();
        if (Objects.nonNull(decisionNode)) {
            if (CollectionUtils.isNotEmpty(decisionNode.getGroupRouteRuleDetailRepDTOList())) {
                detailList.addAll(decisionNode.getGroupRouteRuleDetailRepDTOList());
            }
            if (CollectionUtils.isNotEmpty(decisionNode.getChildrenRulesDecisionNodeList())) {
                for (GroupRouteRulesDecisionReqDTO child : decisionNode.getChildrenRulesDecisionNodeList()) {
                    detailList.addAll(recurveListGroupRuleDetailRepDTOs(child));
                }
            }
        }
        return detailList;
    }

    private List<GroupRouteRulesDecisionDO> listGroupRulesDecisionNodes(GroupRouteRulesDecisionReqDTO groupRouteRulesDecisionReqDTO) {
        List<GroupRouteRulesDecisionReqDTO> groupRouteRulesDecisionReqDTOS = recurveListGroupRulesDecisionReqDTOs(groupRouteRulesDecisionReqDTO);
        return BeanCopyUtils.copyList(groupRouteRulesDecisionReqDTOS, GroupRouteRulesDecisionDO.class);
    }

    private List<GroupRouteRulesDecisionReqDTO> recurveListGroupRulesDecisionReqDTOs(GroupRouteRulesDecisionReqDTO decisionNode) {
        List<GroupRouteRulesDecisionReqDTO> childDecisionNodeList = Lists.newArrayList();
        if (Objects.nonNull(decisionNode)) {
            childDecisionNodeList.add(decisionNode);
            if (CollectionUtils.isNotEmpty(decisionNode.getChildrenRulesDecisionNodeList())) {
                for (GroupRouteRulesDecisionReqDTO child : decisionNode.getChildrenRulesDecisionNodeList()) {
                    childDecisionNodeList.addAll(recurveListGroupRulesDecisionReqDTOs(child));
                }
            }
        }
        return childDecisionNodeList;
    }

    /**
     * 根据id列表修改进件路由规则决策状态
     *
     * @param ids    id列表
     * @param status 状态
     * @return effect rows
     */
    @Override
    public Integer updateGroupRouteRuleStatusByIds(List<Long> ids, Integer status) {
        if (CollectionUtils.isEmpty(ids) || !EnumUtils.ofNullable(ValidStatusEnum.class, status).isPresent()) {
            return 0;
        }
        return groupRouteRulesDecisionDAO.updateValidStatusByIds(new HashSet<>(ids), status);
    }

    /**
     * 查询优先级是否已被占用
     *
     * @param priority 优先级
     * @return 0-未被占用 1-已被占用
     */
    @Override
    public Integer getPriorityUseStatus(Integer priority) {
        return CollectionUtils.isNotEmpty(groupRouteRulesDecisionDAO.listByPriority(priority)) ? UseStatusEnum.IN_USE.getValue() : UseStatusEnum.NO_USE.getValue();
    }

    /**
     * 根据收单机构标识获取进件报备规则组
     *
     * @param acquirer 收单机构
     * @return 报备规则组列表
     */
    @Override
    public List<McRuleGroupRspDTO> listGroupIdsByAcquirer(String acquirer) {
        return BeanCopyUtils.copyList(mcRuleGroupDAO.listByAcquirerAndStatus(acquirer, ValidStatusEnum.VALID.getValue()), McRuleGroupRspDTO.class);
    }
}
