package com.wosai.upay.job.refactor.biz.acquirer.lklv3;

import com.shouqianba.cua.annotation.ITextValueEnum;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.merchant.contract.model.lklV3.MerAccountResp;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * lklV3商户信息处理
 * 商户基本信息，营业执照，帐户等
 *
 * <AUTHOR>
 * @date 2024/7/24 15:13
 */
@Component
public class LklV3MerchantInfoProcessor {

    @Autowired
    private LklV3Service lklV3Service;

    @Resource
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource
    private ContractParamsBiz contractParamsBiz;



    /**
     * 获取商户帐户信息
     *
     * @param merchantSn 商户号
     * @return 商户帐户信息
     */
    public Optional<MerAccountResp> getMerchantBankAccountInfo(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Optional.empty();
        }
        Optional<String> providerMerchantIdOpt = merchantTradeParamsBiz.getMerchantAcquirerMerchantId(merchantSn, AcquirerTypeEnum.LKL_V3);
        if (!providerMerchantIdOpt.isPresent()) {
            return Optional.empty();
        }
        try {
            final LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
            return Optional.ofNullable(lklV3Service.queryMerAccount(providerMerchantIdOpt.get(), lklV3Param));
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    /**
     * 比较银行卡号是否一致
     *
     * @param sqbBankCardNo      收钱吧银行卡号
     * @param acquirerBankCardNo 收单银行卡号
     * @return 是否一致 true 一致 false 不一致
     */
    public boolean compareBankCardNos(String sqbBankCardNo, String acquirerBankCardNo) {
        if (StringUtils.isBlank(sqbBankCardNo) || StringUtils.isBlank(acquirerBankCardNo)) {
            return false;
        }
        // lkl对于10位以下的银行卡脱敏返回有问题
        if (sqbBankCardNo.length() <= 10) {
            return acquirerBankCardNo.length() <= 10;
        }
        if (!Objects.equals(sqbBankCardNo.length(), acquirerBankCardNo.length())) {
            return false;
        }
        // lkl需要比较脱敏的数据 如: 231233345345443243 23123********43243
        for (int i = 0; i < sqbBankCardNo.length(); i++) {
            char normalChar = sqbBankCardNo.charAt(i);
            char maskedChar = acquirerBankCardNo.charAt(i);
            if (maskedChar != '*' && normalChar != maskedChar) {
                return false;
            }
        }
        return true;
    }

}
