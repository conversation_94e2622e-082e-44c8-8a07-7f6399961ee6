package com.wosai.upay.job.refactor.model.bo;

import com.wosai.upay.job.refactor.model.enums.LogicOperatorEnum;
import com.wosai.upay.job.refactor.model.enums.RuleOperatorEnum;
import lombok.Data;

import java.util.List;

/**
 * 规则条件业务对象
 * 
 * 支持构建复杂的业务规则条件，包括：
 * - 单一条件：字段与固定值/其他字段/值列表比较
 * - 逻辑组合：多层嵌套的AND/OR逻辑组合
 * 
 * 注意：为避免JSON序列化时将方法误认为字段，
 * 使用 checkXXX() 方法名而非 isXXX()
 * 
 * =================================================
 * 完整JSON规则样例集合
 * =================================================
 * 
 * 1. 基础单条件 - 字段与固定值比较
 * {
 *   "ruleName": "账户类型检查",
 *   "conditions": [
 *     {
 *       "fieldPath": "new_account_info.type",
 *       "operator": "EQUALS",
 *       "compareValue": 1
 *     }
 *   ]
 * }
 * 
 * 2. 字段与字段比较
 * {
 *   "ruleName": "银行卡号变更检测",
 *   "conditions": [
 *     {
 *       "fieldPath": "new_account_info.cardNumber",
 *       "operator": "NOT_EQUALS",
 *       "compareFieldPath": "old_account_info.cardNumber"
 *     }
 *   ]
 * }
 * 
 * 3. IN/NOT_IN操作（值列表比较）
 * {
 *   "ruleName": "账户类型白名单",
 *   "conditions": [
 *     {
 *       "fieldPath": "new_account_info.type",
 *       "operator": "IN",
 *       "compareValueList": [1, 2, 3]
 *     }
 *   ]
 * }
 * 
 * 4. CONTAINS/IS_NULL操作
 * {
 *   "ruleName": "银行名关键字检查",
 *   "conditions": [
 *     {
 *       "fieldPath": "new_account_info.bankName",
 *       "operator": "CONTAINS",
 *       "compareValue": "工商银行"
 *     }
 *   ]
 * }
 * 
 * 5. 多条件AND组合（默认逻辑）
 * {
 *   "ruleName": "卡号和银行名同时变更",
 *   "conditions": [
 *     {
 *       "fieldPath": "new_account_info.cardNumber",
 *       "operator": "NOT_EQUALS",
 *       "compareFieldPath": "old_account_info.cardNumber"
 *     },
 *     {
 *       "fieldPath": "new_account_info.bankName",
 *       "operator": "NOT_EQUALS",
 *       "compareFieldPath": "old_account_info.bankName"
 *     }
 *   ]
 * }
 * // 逻辑：卡号变更 AND 银行名变更
 * 
 * 6. OR逻辑组合（使用logic字段）
 * {
 *   "ruleName": "卡号或银行名变更",
 *   "conditions": [
 *     {
 *       "logic": "OR",
 *       "conditions": [
 *         {
 *           "fieldPath": "new_account_info.cardNumber",
 *           "operator": "NOT_EQUALS",
 *           "compareFieldPath": "old_account_info.cardNumber"
 *         },
 *         {
 *           "fieldPath": "new_account_info.bankName",
 *           "operator": "NOT_EQUALS",
 *           "compareFieldPath": "old_account_info.bankName"
 *         }
 *       ]
 *     }
 *   ]
 * }
 * // 逻辑：卡号变更 OR 银行名变更
 * 
 * 7. 混合逻辑（AND/OR组合）
 * {
 *   "ruleName": "对公账户变更规则",
 *   "conditions": [
 *     {
 *       "logic": "OR",
 *       "conditions": [
 *         {
 *           "fieldPath": "new_account_info.cardNumber",
 *           "operator": "NOT_EQUALS",
 *           "compareFieldPath": "old_account_info.cardNumber"
 *         },
 *         {
 *           "fieldPath": "new_account_info.bankName",
 *           "operator": "NOT_EQUALS",
 *           "compareFieldPath": "old_account_info.bankName"
 *         }
 *       ]
 *     },
 *     {
 *       "fieldPath": "new_account_info.type",
 *       "operator": "EQUALS",
 *       "compareValue": 1
 *     }
 *   ]
 * }
 * // 逻辑：(卡号变更 OR 银行名变更) AND 账户类型为对公
 * 
 * 8. 复杂三层嵌套逻辑
 * {
 *   "ruleName": "三层嵌套复杂规则",
 *   "conditions": [
 *     {
 *       "logic": "OR",
 *       "conditions": [
 *         {
 *           "logic": "AND",
 *           "conditions": [
 *             {
 *               "logic": "OR",
 *               "conditions": [
 *                 {
 *                   "fieldPath": "new_account_info.cardNumber",
 *                   "operator": "NOT_EQUALS",
 *                   "compareFieldPath": "old_account_info.cardNumber"
 *                 },
 *                 {
 *                   "fieldPath": "new_account_info.bankName",
 *                   "operator": "NOT_EQUALS",
 *                   "compareFieldPath": "old_account_info.bankName"
 *                 }
 *               ]
 *             },
 *             {
 *               "fieldPath": "new_account_info.type",
 *               "operator": "EQUALS",
 *               "compareValue": 1
 *             }
 *           ]
 *         },
 *         {
 *           "logic": "AND",
 *           "conditions": [
 *             {
 *               "fieldPath": "new_account_info.holder",
 *               "operator": "NOT_EQUALS",
 *               "compareFieldPath": "old_account_info.holder"
 *             },
 *             {
 *               "fieldPath": "new_account_info.openingBank",
 *               "operator": "CONTAINS",
 *               "compareValue": "北京"
 *             }
 *           ]
 *         }
 *       ]
 *     }
 *   ]
 * }
 * // 逻辑：((卡号变更 OR 银行名变更) AND 账户类型=1) OR (持卡人变更 AND 开户行包含"北京")
 * 
 * 9. 多种操作符组合示例
 * {
 *   "ruleName": "高风险变更检测",
 *   "conditions": [
 *     {
 *       "fieldPath": "new_account_info.type",
 *       "operator": "IN",
 *       "compareValueList": [1, 2]
 *     },
 *     {
 *       "fieldPath": "new_account_info.bankName",
 *       "operator": "NOT_IN",
 *       "compareValueList": ["工商银行", "建设银行", "中国银行"]
 *     },
 *     {
 *       "fieldPath": "new_account_info.openingBank",
 *       "operator": "IS_NOT_NULL"
 *     },
 *     {
 *       "fieldPath": "new_account_info.clearingBank",
 *       "operator": "CONTAINS",
 *       "compareValue": "102"
 *     }
 *   ]
 * }
 * // 逻辑：账户类型IN[1,2] AND 银行名NOT_IN指定列表 AND 开户行非空 AND 清算行包含"102"
 * 
 * =================================================
 * 重要说明：
 * 1. ruleName字段：必须在根节点设置，用于标识规则
 * 2. logic字段：只在逻辑组条件上设置，控制该组内部条件的逻辑关系
 * 3. 默认逻辑：条件列表默认使用AND逻辑，需要OR时必须显式设置logic字段
 * 4. 嵌套支持：理论上支持无限层嵌套，但建议控制在3层以内
 * 5. 字段路径：支持点分隔的嵌套访问，如 new_account_info.cardNumber
 * =================================================
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
public class RuleConditionBO {

    /**
     * 规则名称
     * 用于描述该规则的业务含义
     * 根节点使用,且不可以为空
     */
    private String ruleName;

    /**
     * 字段路径
     * 支持嵌套字段访问，如：new_account_info.type
     */
    private String fieldPath;

    /**
     * 比较操作符
     * 支持：EQUALS, NOT_EQUALS, GREATER_THAN, LESS_THAN, IN, NOT_IN, CONTAINS等
     */
    private RuleOperatorEnum operator;

    /**
     * 单个比较值
     * 用于与固定值比较的场景
     */
    private Object compareValue;

    /**
     * 比较值列表
     * 用于IN、NOT_IN等需要多个值的操作
     */
    private List<Object> compareValueList;

    /**
     * 比较字段路径
     * 用于字段与字段比较的场景，如：old_account_info.cardNumber
     */
    private String compareFieldPath;

    /**
     * 逻辑操作符
     * 当前条件与下一个条件的逻辑关系（AND/OR）
     * 只在逻辑组合条件中使用
     */
    private LogicOperatorEnum logic;

    /**
     * 子条件列表
     * 用于构建复杂的逻辑组合，支持嵌套
     * 只在逻辑组合条件中使用
     */
    private List<RuleConditionBO> conditions;

    /**
     * 检查是否为逻辑组合条件
     * 逻辑组合条件包含logic和conditions字段
     */
    public boolean checkLogicGroup() {
        return logic != null && conditions != null && !conditions.isEmpty();
    }

    /**
     * 检查是否为单一条件
     * 单一条件包含field和operator字段
     */
    public boolean checkSingleCondition() {
        return fieldPath != null && operator != null;
    }

    /**
     * 是否有单个比较值
     */
    public boolean hasCompareValue() {
        return compareValue != null;
    }

    /**
     * 是否有比较值列表
     */
    public boolean hasCompareValueList() {
        return compareValueList != null && !compareValueList.isEmpty();
    }

    /**
     * 是否有比较字段路径
     */
    public boolean hasCompareFieldPath() {
        return compareFieldPath != null && !compareFieldPath.trim().isEmpty();
    }

    /**
     * 检查是否为根节点
     * 根节点包含规则名称
     */
    public boolean checkRootNode() {
        return ruleName != null;
    }

    /**
     * 检查是否只有规则元数据
     * 用于判断是否为纯粹的根节点容器
     */
    public boolean checkRuleMetadataOnly() {
        return ruleName != null && 
               fieldPath == null && operator == null && logic == null;
    }
}