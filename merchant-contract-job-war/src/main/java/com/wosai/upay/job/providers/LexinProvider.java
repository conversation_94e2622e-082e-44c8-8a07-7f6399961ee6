package com.wosai.upay.job.providers;

import java.util.Collections;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * 乐信渠道处理类
 */
@Component(ProviderUtil.LEXIN_CHANNEL)
@Slf4j
@Transactional
public class LexinProvider extends AbstractProvider {
    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event,
        Map<String, Object> paramContext, ContractRule contractRule) {
        return null;
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel,
        ContractSubTask sub) {
        return null;
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel,
        ContractSubTask sub) {
        return null;
    }

    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        return Collections.emptyMap();
    }
}
