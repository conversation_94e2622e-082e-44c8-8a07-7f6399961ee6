package com.wosai.upay.job.refactor.task.license.micro.builder.config;

import com.wosai.upay.job.biz.IndustryMappingCommonBiz;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.refactor.task.license.micro.builder.processor.PaywayProcessorFactory;
import com.wosai.upay.job.refactor.task.license.micro.builder.processor.acquirer.AcquirerPaywayProcessor;
import com.wosai.upay.job.refactor.task.license.micro.builder.processor.alipay.AlipayPaywayProcessor;
import com.wosai.upay.job.refactor.task.license.micro.builder.processor.unionpay.UnionpayPaywayProcessor;
import com.wosai.upay.job.refactor.task.license.micro.builder.processor.weixin.WeixinPaywayProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 交易参数构建器配置类
 * 负责注册所有的支付方式处理器
 * 
 * <AUTHOR>
 */
@Configuration
public class TradeParamsBuilderConfig {
    
    @Autowired
    private IndustryMappingCommonBiz industryMappingCommonBiz;
    
    @Autowired
    private WechatAuthBiz wechatAuthBiz;
    
    @PostConstruct
    public void init() {
        // 注册拉卡拉V3处理器
        registerLklV3Processors();
        
        // 注册富友处理器
        registerFuyouProcessors();
        
        // 注册海科处理器
        registerHaikeProcessors();
    }
    
    private void registerLklV3Processors() {
        String acquirerType = "LKLV3";
        PaywayProcessorFactory.registerProcessor(new AcquirerPaywayProcessor(acquirerType));
        PaywayProcessorFactory.registerProcessor(createAlipayProcessor(acquirerType));
        PaywayProcessorFactory.registerProcessor(createWeixinProcessor(acquirerType));
        PaywayProcessorFactory.registerProcessor(createUnionpayProcessor(acquirerType));
    }
    
    private void registerFuyouProcessors() {
        String acquirerType = "FUYOU";
        PaywayProcessorFactory.registerProcessor(new AcquirerPaywayProcessor(acquirerType));
        PaywayProcessorFactory.registerProcessor(createAlipayProcessor(acquirerType));
        PaywayProcessorFactory.registerProcessor(createWeixinProcessor(acquirerType));
        PaywayProcessorFactory.registerProcessor(createUnionpayProcessor(acquirerType));
    }
    
    private void registerHaikeProcessors() {
        String acquirerType = "HAIKE";
        PaywayProcessorFactory.registerProcessor(new AcquirerPaywayProcessor(acquirerType));
        PaywayProcessorFactory.registerProcessor(createAlipayProcessor(acquirerType));
        PaywayProcessorFactory.registerProcessor(createWeixinProcessor(acquirerType));
        PaywayProcessorFactory.registerProcessor(createUnionpayProcessor(acquirerType));
    }
    
    private AlipayPaywayProcessor createAlipayProcessor(String acquirerType) {
        AlipayPaywayProcessor processor = new AlipayPaywayProcessor(acquirerType);
        // 手动注入依赖
        processor.setIndustryMappingCommonBiz(industryMappingCommonBiz);
        processor.setWechatAuthBiz(wechatAuthBiz);
        return processor;
    }
    
    private WeixinPaywayProcessor createWeixinProcessor(String acquirerType) {
        WeixinPaywayProcessor processor = new WeixinPaywayProcessor(acquirerType);
        // 手动注入依赖
        processor.setWechatAuthBiz(wechatAuthBiz);
        return processor;
    }
    
    private UnionpayPaywayProcessor createUnionpayProcessor(String acquirerType) {
        UnionpayPaywayProcessor processor = new UnionpayPaywayProcessor(acquirerType);
        // 手动注入依赖
        processor.setWechatAuthBiz(wechatAuthBiz);
        return processor;
    }
}
