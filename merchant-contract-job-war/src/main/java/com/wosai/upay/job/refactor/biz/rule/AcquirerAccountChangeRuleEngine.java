package com.wosai.upay.job.refactor.biz.rule;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.refactor.model.bo.RuleConditionBO;
import com.wosai.upay.job.refactor.model.bo.RuleEvaluationContext;
import com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO;
import com.wosai.upay.job.refactor.model.enums.LogicOperatorEnum;
import com.wosai.upay.job.refactor.model.enums.RuleOperatorEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 收单机构账户变更规则引擎
 * 负责解析和执行JSON格式的规则逻辑，支持复杂的条件组合
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Component
@Slf4j
public class AcquirerAccountChangeRuleEngine {

    /**
     * 评估规则
     * 
     * @param record 换卡记录
     * @param ruleLogic 规则逻辑JSON
     * @return 是否满足规则
     */
    public boolean evaluate(AcquirerAccountChangeRecordDO record, String ruleLogic) {
        try {
            RuleEvaluationContext context = new RuleEvaluationContext(record);
            
            if (StringUtils.isEmpty(ruleLogic)) {
                return isCardNumberChanged(context);
            }
            
            RuleConditionBO rule = JSON.parseObject(ruleLogic, RuleConditionBO.class);
            
            // 如果是根节点且只包含元数据，则评估其子条件
            if (rule.checkRuleMetadataOnly() && rule.getConditions() != null) {
                return evaluateConditions(context, rule.getConditions());
            }
            
            // 如果是单个条件或逻辑组合，直接评估
            return evaluateSingleConditionOrGroup(context, rule);
            
        } catch (Exception e) {
            log.error("Evaluate rule error, ruleLogic: {}", ruleLogic, e);
            return false;
        }
    }
    
    /**
     * 默认规则：判断卡号是否变更
     */
    private boolean isCardNumberChanged(RuleEvaluationContext context) {
        try {
            Object oldCardNumber = context.extractFieldValue("old_account_info.cardNumber");
            Object newCardNumber = context.extractFieldValue("new_account_info.cardNumber");
            return !Objects.equals(oldCardNumber, newCardNumber);
        } catch (Exception e) {
            log.error("Check card number changed error", e);
            return true; // 默认计数
        }
    }
    
    /**
     * 评估条件组
     */
    private boolean evaluateConditions(RuleEvaluationContext context, List<RuleConditionBO> conditions) {
        if (conditions == null || conditions.isEmpty()) {
            return true;
        }
        
        // 先检查是否有OR逻辑，决定初始值和整体逻辑
        boolean hasOrLogic = conditions.stream()
                .anyMatch(condition -> LogicOperatorEnum.OR.equals(condition.getLogic()));
        
        if (hasOrLogic) {
            return evaluateConditionsWithMixedLogic(context, conditions);
        } else {
            return evaluateConditionsWithAndLogic(context, conditions);
        }
    }
    
    /**
     * 处理纯AND逻辑的条件组
     */
    private boolean evaluateConditionsWithAndLogic(RuleEvaluationContext context, List<RuleConditionBO> conditions) {
        for (RuleConditionBO condition : conditions) {
            boolean conditionResult = evaluateSingleConditionOrGroup(context, condition);
            if (!conditionResult) {
                return false; // 短路与：任一为假则整体为假
            }
        }
        return true;
    }
    
    /**
     * 处理混合逻辑的条件组（包含OR）
     * 这个方法实际上不应该被调用，因为每个逻辑组都有明确的logic字段
     * 保留作为备用，默认使用AND逻辑
     */
    private boolean evaluateConditionsWithMixedLogic(RuleEvaluationContext context, List<RuleConditionBO> conditions) {
        log.warn("Mixed logic evaluation called, this should not happen with proper rule structure");
        return evaluateConditionsWithAndLogic(context, conditions);
    }
    
    /**
     * 评估单个条件或条件组（支持完整递归）
     */
    private boolean evaluateSingleConditionOrGroup(RuleEvaluationContext context, RuleConditionBO condition) {
        if (condition.checkLogicGroup()) {
            // 递归处理嵌套的逻辑组合条件
            return evaluateNestedLogicGroup(context, condition);
        } else if (condition.checkSingleCondition()) {
            return evaluateCondition(context, condition);
        } else if (condition.getConditions() != null && !condition.getConditions().isEmpty()) {
            // 纯粹的条件容器（无logic字段），默认AND逻辑
            return evaluateConditions(context, condition.getConditions());
        } else {
            log.warn("Invalid condition: neither logic group nor single condition, condition: {}", condition);
            return true; // 无效条件默认为真
        }
    }
    
    /**
     * 评估嵌套的逻辑组合（递归处理核心方法）
     */
    private boolean evaluateNestedLogicGroup(RuleEvaluationContext context, RuleConditionBO logicGroup) {
        LogicOperatorEnum logic = logicGroup.getLogic();
        List<RuleConditionBO> conditions = logicGroup.getConditions();
        
        if (conditions == null || conditions.isEmpty()) {
            log.warn("Logic group has no conditions, logic: {}", logic);
            return true;
        }
        
        log.debug("Evaluating nested logic group, logic: {}, conditions count: {}", logic, conditions.size());
        
        if (LogicOperatorEnum.OR.equals(logic)) {
            // OR逻辑：任一子条件为真则整体为真
            for (RuleConditionBO condition : conditions) {
                boolean conditionResult = evaluateSingleConditionOrGroup(context, condition);
                if (conditionResult) {
                    log.debug("OR logic short-circuit: condition passed");
                    return true; // 短路或
                }
            }
            return false; // 所有条件都为假
        } else {
            // AND逻辑或默认逻辑：所有子条件都为真才整体为真
            for (RuleConditionBO condition : conditions) {
                boolean conditionResult = evaluateSingleConditionOrGroup(context, condition);
                if (!conditionResult) {
                    log.debug("AND logic short-circuit: condition failed");
                    return false; // 短路与
                }
            }
            return true; // 所有条件都为真
        }
    }
    
    /**
     * 评估单个条件
     */
    private boolean evaluateCondition(RuleEvaluationContext context, RuleConditionBO condition) {
        String field = condition.getFieldPath();
        RuleOperatorEnum operator = condition.getOperator();
        
        // 获取字段值
        Object fieldValue = context.extractFieldValue(field);
        
        // 获取比较值
        Object compareValue;
        if (condition.hasCompareFieldPath()) {
            // 字段与字段比较
            compareValue = context.extractFieldValue(condition.getCompareFieldPath());
        } else if (condition.hasCompareValue()) {
            // 字段与固定值比较
            compareValue = condition.getCompareValue();
        } else if (condition.hasCompareValueList()) {
            // 字段与值列表比较（IN/NOT_IN操作）
            compareValue = condition.getCompareValueList();
        } else {
            log.warn("Invalid condition: no compare value specified, field: {}, operator: {}", 
                    field, operator);
            return false;
        }
        
        // 执行比较
        return compare(fieldValue, compareValue, operator);
    }
    

    
    /**
     * 比较值
     */
    private boolean compare(Object fieldValue, Object compareValue, RuleOperatorEnum operator) {
        if (fieldValue == null) {
            return RuleOperatorEnum.IS_NULL.equals(operator);
        }
        
        switch (operator) {
            case EQUALS:
                return Objects.equals(fieldValue, compareValue);
            case NOT_EQUALS:
                return !Objects.equals(fieldValue, compareValue);
            case GREATER_THAN:
                return compareNumbers(fieldValue, compareValue) > 0;
            case GREATER_THAN_OR_EQUAL:
                return compareNumbers(fieldValue, compareValue) >= 0;
            case LESS_THAN:
                return compareNumbers(fieldValue, compareValue) < 0;
            case LESS_THAN_OR_EQUAL:
                return compareNumbers(fieldValue, compareValue) <= 0;
            case IN:
                return inList(fieldValue, compareValue);
            case NOT_IN:
                return !inList(fieldValue, compareValue);
            case CONTAINS:
                return contains(fieldValue, compareValue);
            case NOT_CONTAINS:
                return !contains(fieldValue, compareValue);
            case IS_NULL:
            case IS_NULL_ALIAS:
                return fieldValue == null;
            case IS_NOT_NULL:
            case IS_NOT_NULL_ALIAS:
                return fieldValue != null;
            default:
                log.warn("Unknown operator: {}", operator);
                return false;
        }
    }
    
    /**
     * 比较数字
     */
    private int compareNumbers(Object value1, Object value2) {
        try {
            BigDecimal num1 = new BigDecimal(value1.toString());
            BigDecimal num2 = new BigDecimal(value2.toString());
            return num1.compareTo(num2);
        } catch (Exception e) {
            log.error("Compare numbers error, value1: {}, value2: {}", value1, value2, e);
            return 0;
        }
    }
    
    /**
     * 判断值是否在列表中
     */
    private boolean inList(Object fieldValue, Object compareValue) {
        try {
            if (compareValue instanceof Collection) {
                Collection<?> collection = (Collection<?>) compareValue;
                return collection.contains(fieldValue);
            }
            return false;
        } catch (Exception e) {
            log.error("Check in list error, fieldValue: {}, compareValue: {}", fieldValue, compareValue, e);
            return false;
        }
    }
    
    /**
     * 判断是否包含
     */
    private boolean contains(Object fieldValue, Object compareValue) {
        try {
            if (fieldValue == null || compareValue == null) {
                return false;
            }
            return fieldValue.toString().contains(compareValue.toString());
        } catch (Exception e) {
            log.error("Check contains error, fieldValue: {}, compareValue: {}", fieldValue, compareValue, e);
            return false;
        }
    }
}