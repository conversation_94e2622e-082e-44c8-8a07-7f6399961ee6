package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.IdentificationTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.crow.api.service.TagIngestService;
import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.event.terminal.basic.TerminalBasicInsertEvent;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.common.helper.MyObjectMapper;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.acquirer.AcquirerChangeCheckBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.biz.store.StoreBiz;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.externalservice.coreb.TradeConfigClient;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.model.dto.WeixinSubDevResp;
import com.wosai.upay.job.refactor.client.PartnerMerchantInfoClient;
import com.wosai.upay.job.repository.ProviderTerminalTaskRepository;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.constant.LakalaBusinessFileds;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinAppidConfig;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.guangfa.response.GFAprvlrsltqryRes;
import com.wosai.upay.merchant.contract.model.terminal.*;
import com.wosai.upay.merchant.contract.service.UnionService;
import com.wosai.upay.side.service.GeneralRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.job.refactor.task.license.update.LicenseUpdateToAcquirerTaskHandler.LICENSE_UPDATE_V2_NEED_UPDATE_ACCOUNT_KEY;


/**
 * <AUTHOR>
 * @date 2019-07-05
 */
@Slf4j
public abstract class AbstractProvider implements BasicProvider {

    @Autowired
    protected ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private PayForProvider payForProvider;
    @Autowired
    private GeneralRuleService generalRuleService;
    @Autowired
    private DataBusBiz dataBusBiz;
    @Autowired
    TradeConfigService tradeConfigService;
    @Autowired
    protected ComposeAcquirerBiz acquirerBiz;
    @Autowired
    protected AgentAppidBiz agentAppidBiz;
    @Autowired
    protected UnionService unionService;
    @Autowired
    private AcquirerChangeCheckBiz acquirerChangeCheckBiz;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Autowired
    protected DefaultChangeTradeParamsBiz tradeParamsBiz;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    StoreBiz storeBiz;
    @Autowired
    TerminalService terminalService;
    @Autowired
    MerchantService merchantService;

    @Autowired
    ProviderTerminalIdBiz providerTerminalIdBiz;
    @Autowired
    ProviderTerminalBiz providerTerminalBiz;
    @Autowired
    ProviderTerminalTaskRepository providerTerminalTaskRepository;
    @Autowired
    StoreService storeService;

    @Autowired
    TagIngestService tagIngestService;
    @Autowired
    private SubBizParamsBiz subBizParamsBiz;
    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    protected ContractParamsBiz contractParamsBiz;

    @Resource
    private PartnerMerchantInfoClient partnerMerchantInfoClient;


    @Autowired
    private TradeConfigClient tradeConfigClient;

    public boolean shouldPayFor(ContractTask contractTask, ContractSubTask contractSubTask, Map bank) {
        // 全来店商户不需要代付
        if (partnerMerchantInfoClient.isQldMerchant(contractTask.getMerchant_sn())) {
            return false;
        }
        int type = BeanUtil.getPropInt(bank, MerchantBankAccount.TYPE);
        int idType = BeanUtil.getPropInt(bank, MerchantBankAccount.ID_TYPE);
        // 商户信息变更中可能会涉及到换卡
        if (ProviderUtil.MERCHANT_BUSINESS_DATA_CHANGE.equals(contractTask.getType())
                && contractSubTask.getTask_type().equals(ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE)) {
            return type == BankAccountTypeEnum.PUBLIC.getValue() || (type == BankAccountTypeEnum.PERSONAL.getValue() && idType != IdentificationTypeEnum.PRC_ID_CARD.getValue());
        }
        if (ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(contractSubTask.getTask_type())) {
            // 如果是进件任务  银行卡对公  或者  银行卡是对私但是持有人证件信息不是身份证  需要代付
            return type == BankAccountTypeEnum.PUBLIC.getValue() || (type == BankAccountTypeEnum.PERSONAL.getValue() && idType != IdentificationTypeEnum.PRC_ID_CARD.getValue());
        }
        // 银行卡是对私 但是持有人证件信息不是身份证  需要代付
        return type == BankAccountTypeEnum.PERSONAL.getValue() && idType != IdentificationTypeEnum.PRC_ID_CARD.getValue();
    }

    @Override
    public ContractSubTask produceTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        boolean hasContract = hasContract(event, contractRule);
        // 未进件且配置允许进件
        if (!hasContract && contractRule.getIs_insert()) {
            return produceInsertTaskByRule(merchantSn, event, paramContext, contractRule);
        }

        // 已进件且配置允许更新
        if (hasContract && contractRule.getIs_update()) {
            return produceUpdateTaskByRule(merchantSn, event, paramContext, contractRule);
        }

        return null;
    }

    /**
     * 处理增网增终新增任务
     *
     * @param event
     * @return
     */
    public void produceInsertTerminalTaskByRule(TerminalBasicInsertEvent event, MerchantInfo merchant) {
    }

    /**
     * 处理新增任务
     *
     * @param merchantSn
     * @param event
     * @param paramContext
     * @param contractRule
     * @return
     */
    protected ContractSubTask produceInsertTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(contractRule.getInsertInfluPtask())
                .setChannel(getProviderBeanName())
                .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT)
                .setChange_config(contractRule.getDefault())
                .setDefault_channel(contractRule.getDefault())
                .setPayway(contractRule.getPayway())
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setRetry(0);
        return subTask;
    }

    /**
     * 获取 ProviderBeanName
     *
     * @return
     */
    String getProviderBeanName() {
        String[] beanNames = applicationContext.getBeanNamesForType(this.getClass());
        if (beanNames.length == 1) {
            return beanNames[0];
        }
        throw new ContractBizException(getClass().getSimpleName() + " 未指定处理BeanName");
    }

    /**
     * 处理更新任务
     *
     * @param merchantSn
     * @param event
     * @param paramContext
     * @param contractRule
     * @return
     */
    protected abstract ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule);


    @Override
    public ContractResponse processTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask contractSubTask) {
        boolean insert = ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(contractSubTask.getTask_type());
        if (insert) {
            return processInsertTaskByRule(contractTask, contractChannel, contractSubTask);
        }
        return processUpdateTaskByRule(contractTask, contractChannel, contractSubTask);
    }

    protected abstract ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub);


    protected abstract ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub);


    @Override
    public WeixinSubDevResp weixinSubDevConfig(MerchantProviderParams merchantProviderParams) {
        WeixinConfig weixinConfig = agentAppidBiz.getConfig(merchantProviderParams.getMerchant_sn(), merchantProviderParams.getProvider(), merchantProviderParams.getChannel_no(), merchantProviderParams.getPay_merchant_id());
        return weixinSubDevConfig(weixinConfig, merchantProviderParams);
    }

    /**
     * 微信子商户配置公众号
     *
     * @param appid
     * @param merchantProviderParams
     * @return
     */
    public WeixinSubDevResp weixinSubDevConfig(String appid, MerchantProviderParams merchantProviderParams) {
        return weixinSubDevConfig(buildWeixinConfig(merchantProviderParams.getPay_merchant_id(), appid), merchantProviderParams);
    }

    private WeixinConfig buildWeixinConfig(String mchId, String appid) {
        WeixinConfig weixinConfig = new WeixinConfig();
        List<WeixinAppidConfig> appidConfigs = new ArrayList<>();

        WeixinAppidConfig weixinAppidConfig = new WeixinAppidConfig();
        weixinAppidConfig.setSub_appid(appid);
        weixinAppidConfig.setMini(true);
        appidConfigs.add(weixinAppidConfig);

        weixinConfig.setAppidConfigs(appidConfigs);
        weixinConfig.setWeixinMchId(mchId);
        return weixinConfig;
    }

    @Override
    public WeixinSubDevResp weixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        if (Objects.isNull(weixinConfig) ||
                (WosaiCollectionUtils.isEmpty(weixinConfig.getAppidConfigs()) && WosaiCollectionUtils.isEmpty(weixinConfig.getPayAuthPath())) ||
                !Objects.equals(merchantProviderParams.getPayway(), PaywayEnum.WEIXIN.getValue())
        ) {
            return WeixinSubDevResp.NOT_NEED_CONFIG;
        }
        Map result = doWeixinSubDevConfig(weixinConfig, merchantProviderParams);
        if (CommonModel.RESULT_CODE_SUCCESS.equals(BeanUtil.getPropString(result, CommonModel.RESULT_CODE))) {
            return WeixinSubDevResp.CONFIG_SUCCESS;
        }
        return WeixinSubDevResp.fail(BeanUtil.getPropString(result, CommonModel.RESULT_MESSAGE));
    }

    protected abstract Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams);

    /**
     * 商户是否在指定渠道下进件成功
     *
     * @param event
     * @param contractRule
     * @return
     */
    protected boolean hasContract(ContractEvent event, ContractRule contractRule) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or()
                .andMerchant_snEqualTo(event.getMerchant_sn())
                .andChannel_noEqualTo(contractRule.getChannelNo())
                .andPaywayEqualTo(contractRule.getPayway())
                .andProviderEqualTo(Integer.valueOf(contractRule.getProvider()))
                .andDeletedEqualTo(false);

        List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectByExample(example);
        return WosaiCollectionUtils.isNotEmpty(merchantProviderParams);
    }


    public <T> T buildParam(ContractChannel contractChannel, ContractSubTask sub, Class<T> tClass) {
        return contractParamsBiz.buildParam(contractChannel, sub, tClass);
    }

    protected boolean needPayFor(Map contextParam, ContractSubTask contractSubTask, ContractTask contractTask) {
        PayForTask payForTask = payFor(contextParam, contractTask, contractSubTask);
        if (payForTask != null) {
            if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(contractTask.getType())) {
                dataBusBiz.insert(1, contractTask.getMerchant_sn(), null);
            }
            //将进件任务变为不可调度
            Map subTaskResult = JSON.parseObject(contractSubTask.getResult(), Map.class);
            if (subTaskResult == null) {
                subTaskResult = CollectionUtil.hashMap("channel", ProviderUtil.PAY_FOR_CHANNEL, "message", "代付验证中");
            } else {
                subTaskResult.put("channel", ProviderUtil.PAY_FOR_CHANNEL);
                subTaskResult.put("message", "代付验证中");
            }
            contractSubTask.setSchedule_status(ScheduleEnum.SCHEDULE_DISABLE.getValue()).setResult(JSON.toJSONString(subTaskResult)).setStatus(0);
            contractSubTask.setSchedule_dep_task_id(payForTask.getId());
            contractSubTaskMapper.updateByPrimaryKey(contractSubTask);

            //更新预计审核完成时间
            Date reviewComplte = contractTask.getComplete_at();
            if (reviewComplte == null) {
                contractTask.setComplete_at(StringUtil.parseDate(generalRuleService.getFirstWeekDayAfterDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))));
            } else {
                contractTask.setComplete_at(StringUtil.parseDate(generalRuleService.getFirstWeekDayAfterDate(StringUtil.formatDate(reviewComplte))));
            }
            contractTask.setStatus(TaskStatus.PAY_FOR_WAIT.getVal());

            Map taskResult = JSON.parseObject(contractTask.getResult(), Map.class);
            //这一段单纯是为了文案提示的转义  《--
            Map merchantBankAccount = (Map) contextParam.get(ParamContextBiz.KEY_BANK_ACCOUNT);
            int type = BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.TYPE);
            int idType = BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.ID_TYPE);
            String message = "代付验证中";
            if (type == BankAccountTypeEnum.PERSONAL.getValue() && idType != IdentificationTypeEnum.PRC_ID_CARD.getValue()) {
                message = ScheduleUtil.TIPS_PAY_FOR_PROCESS_PRIVATE_FOREIGN;
            } else if (type == BankAccountTypeEnum.PUBLIC.getValue()) {
                message = ScheduleUtil.TIPS_PAY_FOR_PROCESS_PUBLIC;
            }
            //--》
            if (taskResult == null) {
                taskResult = CollectionUtil.hashMap("channel", ProviderUtil.PAY_FOR_CHANNEL, "message", message);
            } else {
                taskResult.put("channel", ProviderUtil.PAY_FOR_CHANNEL);
                taskResult.put("message", message);
            }
            contractTask.setResult(JSON.toJSONString(taskResult));
            contractTaskBiz.update(contractTask);
            return true;
        }
        return false;
    }

    /**
     * 设置清算通道  2：拉卡拉   3：通联 4:银联商务
     */
    protected void updateClearProvider(String merchantId, String merchantSn) {
        tradeConfigClient.updateClearProvider(merchantId, merchantSn);
    }


    private PayForTask payFor(Map<String, Object> contextParam, ContractTask contractTask, ContractSubTask contractSubTask) {
        if (acquirerChangeCheckBiz.acquirerChange(contractSubTask.getRule_group_id())) {
            return null;
        }
        Map merchantBankAccount = (Map) contextParam.get(ParamContextBiz.KEY_BANK_ACCOUNT);
        if (shouldPayFor(contractTask, contractSubTask, merchantBankAccount)) {
            return payForProvider.produceTask(contextParam, contractSubTask);
        }
        return null;
    }

    @Override
    public boolean changeTradeParams(MerchantProviderParams merchantProviderParams, String fee, boolean sync, String tradeAppId) {
        return tradeParamsBiz.changeTradeParams(merchantProviderParams, fee, sync, tradeAppId);
    }

    @Override
    public boolean openSmartTradeParams(MerchantProviderParams merchantProviderParams, String fee, boolean sync, String tradeAppId) {
        return tradeParamsBiz.changeTradeParamsWithoutCheckAcquirer(merchantProviderParams, fee, sync, tradeAppId);
    }


    @Override
    public HandleQueryStatusResp queryAndHandleContractStatus(ContractSubTask contractSubTask) {
        ContractResponse contractResponse = queryContractStatus(contractSubTask);
        if (contractResponse == null || contractResponse.isBusinessFail()) {
            return new HandleQueryStatusResp()
                    .setFail(true)
                    .setMessage(contractResponse.getMessage());
        } else if (contractResponse.isSystemFail()) {
            return new HandleQueryStatusResp()
                    .setRetry(true)
                    .setMessage(contractResponse.getMessage());
        }
        return handleContractStatus(contractSubTask, contractResponse);
    }

    protected ContractResponse queryContractStatus(ContractSubTask contractSubTask) {
        return new ContractResponse()
                .setCode(400)
                .setMessage("当前通道不支持主动查询进件状态");
    }

    protected HandleQueryStatusResp handleContractStatus(ContractSubTask contractSubTask, ContractResponse contractResponse) {
        return new HandleQueryStatusResp()
                .setFail(true)
                .setMessage("当前通道不支持主动查询进件状态");
    }

    @Override
    public Boolean checkBankContractToBeSigned(ContractSubTask contractSubTask) {
        if (StringUtils.isEmpty(contractSubTask.getContract_id())) {
            log.info("子任务Id:{}还未进件成功", contractSubTask.getId());
            return Boolean.FALSE;
        }
        ContractResponse contractResponse = queryContractStatus(contractSubTask);
        if (contractResponse == null || contractResponse.isBusinessFail()) {
            throw new CommonPubBizException("业务异常:" + Optional.ofNullable(contractResponse).orElseGet(ContractResponse::new).getMessage());
        }
        if (Objects.equals(contractSubTask.getChannel(), "cgb")) {
            //由于广发处于待签约code会返回500
            final Map<String, Object> responseParam = contractResponse.getResponseParam();
            final GFAprvlrsltqryRes gfAprvlrsltqryRes = new MyObjectMapper().convertValue(responseParam, GFAprvlrsltqryRes.class);
            //11-待商户电签
            final String apprvResult = gfAprvlrsltqryRes.getApprvResult();
            final boolean equals = Objects.equals(apprvResult, "11");
            if (equals) {
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        }
        if (contractResponse.isSystemFail()) {
            throw new CommonPubBizException("系统异常" + Optional.ofNullable(contractResponse).orElseGet(ContractResponse::new).getMessage());
        }
        //除了广发外的银行通道自己实现是否处于签约中
        return doCheck(contractSubTask, contractResponse);
    }

    public Boolean doCheck(ContractSubTask contractSubTask, ContractResponse contractResponse) {
        throw new CommonPubBizException("子类还未实现自定义校验");
    }

    @Override
    public ContractResponse addTerminal(ContractTask task, ContractSubTask subTask) {
        return new ContractResponse()
                .setCode(400)
                .setMessage("当前通道不支持新增终端");
    }

    @Override
    public ContractResponse boundTerminal(AddTermInfoDTO addTermInfoDTO, int payWay, String terminalSn) {
        return new ContractResponse()
                .setCode(400)
                .setMessage("当前通道不支持绑定终端");
    }

    @Override
    public ContractResponse unbindTerminal(LogOutTermInfoDTO dto, int payWay, String terminalSn) {
        return new ContractResponse()
                .setCode(400)
                .setMessage("当前通道不支持绑定终端");
    }

    @Override
    public ContractResponse updateTerminal(UpdateTermInfoDTO dto, int payWay, String terminalSn) {
        return new ContractResponse()
                .setCode(400)
                .setMessage("当前通道不支持绑定终端");
    }

    @Override
    public ContractResponse updateTerminalWithCustom(UpdateTermInfoDTO dto,
                                                     int payWay,
                                                     String terminalSn,
                                                     AliTermInfoRequest customAliTermInfo,
                                                     WxTermInfoRequest wxCustomInfo
    ) {
        return new ContractResponse()
                .setCode(400)
                .setMessage("当前通道不支持终端更新");
    }


    /**
     * 收钱吧终端绑定
     *
     * @param merchant 商户信息
     * @param terminal 收钱吧终端信息
     * @param provider 收单机构provider
     */
    @Override
    public void handleSqbTerminalBind(MerchantInfo merchant, Map terminal, Integer provider) {

    }

    /**
     * 终端解绑
     *
     * @param merchant 商户信息
     * @param terminal 收钱吧终端信息
     * @param provider 收单机构provider
     */
    @Override
    public void handleSqbTerminalUnBind(MerchantInfo merchant, Map terminal, Integer provider) {

    }


    /**
     * 收钱吧门店绑定
     *
     * @param storeSn    门店号
     * @param merchantSn 商户号
     * @param provider   收单机构provider
     */
    @Override
    public void handleSqbStoreTerminal(String storeSn, String merchantSn, Integer provider) {

    }

    /**
     * 获取特定provider下的所有间连参数
     *
     * @param provider
     * @param merchantSn
     * @return
     */
    public List<MerchantProviderParams> getMerchantProviderParamsByProvider(Integer provider, String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayIn(Objects.equals(provider, ProviderEnum.PROVIDER_TONGLIAN.getValue()) || Objects.equals(provider, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue()) || Objects.equals(provider, ProviderEnum.PROVIDER_HXB.getValue())
                        || Objects.equals(provider, ProviderEnum.PROVIDER_LAKALA_V3.getValue()) ? Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue(), PaywayEnum.UNIONPAY.getValue()) : Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()))
                .andProviderNotIn(Lists.newArrayList(ProviderEnum.ALI_PAY.getValue(), ProviderEnum.WEI_XIN.getValue()))
                .andProviderIn(Objects.equals(provider, ProviderEnum.PROVIDER_LAKALA_V3.getValue()) ? Lists.newArrayList(ProviderEnum.PROVIDER_DIRECT_UNIONPAY.getValue(), ProviderEnum.PROVIDER_LKLORG.getValue(), ProviderEnum.PROVIDER_LKL_OPEN.getValue()) : Lists.newArrayList(provider))
                .andDeletedEqualTo(false);
        return merchantProviderParamsMapper.selectByExample(example);
    }

    /**
     * 获取特定provider下的所有间连参数
     *
     * @param merchantSn
     * @return
     */
    public List<MerchantProviderParams> getMerchantProviderParamsByProvider(Integer provider, Integer payway, String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(payway)
                .andProviderEqualTo(provider)
                .andDeletedEqualTo(false);
        return merchantProviderParamsMapper.selectByExample(example);
    }

    @Override
    public void createProviderTerminal(String merchantSn, Integer provider) {

    }

    @Override
    public List getFeeRate(String merchantId) {
        if (StringUtil.empty(merchantId)) {
            return null;
        }
        List<Map> merchantConfigs = tradeConfigService.getAnalyzedMerchantConfigs(merchantId);
        Map weixinConfig = null;
        Map alipayConfig = null;
        Map jdConfig = null;
        Map qqConfig = null;
        Map lklWalletConfig = null;
        Map unionConfig = null;
        Map bestPayConfig = null;
        for (Map merchantConfig : merchantConfigs) {
            int payway = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY);
            if (payway == PaywayEnum.ALIPAY.getValue()) {
                alipayConfig = merchantConfig;
            } else if (payway == PaywayEnum.WEIXIN.getValue()) {
                weixinConfig = merchantConfig;
            } else if (payway == PaywayEnum.JD_WALLET.getValue()) {
                jdConfig = merchantConfig;
            } else if (payway == PaywayEnum.QQ_WALLET.getValue()) {
                qqConfig = merchantConfig;
            } else if (payway == PaywayEnum.LAKALA_WALLET.getValue()) {
                lklWalletConfig = merchantConfig;
            } else if (payway == PaywayEnum.UNIONPAY.getValue()) { //银联二维码支付
                unionConfig = merchantConfig;
            } else if (payway == PaywayEnum.BESTPAY.getValue()) {
                bestPayConfig = merchantConfig;
            }
        }

        if (weixinConfig == null || alipayConfig == null) {
            throw new ContextParamException("商户的支付宝或者微信交易配置merchant_config未配置");
        }

        String alipayFeerate = getLadderFee(alipayConfig, MerchantConfig.WAP_FEE_RATE);
        String weixinFeerate = getLadderFee(weixinConfig, MerchantConfig.WAP_FEE_RATE);
        String jdFeerate = getLadderFee(jdConfig, MerchantConfig.WAP_FEE_RATE);
        String qqFeerate = getLadderFee(qqConfig, MerchantConfig.WAP_FEE_RATE);
        String lklWalletFeeRate = getLadderFee(lklWalletConfig, MerchantConfig.B2C_FEE_RATE);
        String unionFeeRate = getLadderFee(unionConfig, MerchantConfig.WAP_FEE_RATE);
        String bestPayFeeRate = getLadderFee(bestPayConfig, MerchantConfig.B2C_FEE_RATE);
        List list = Arrays.asList(
                CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.WECHAT_PAY_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(weixinFeerate)
                ), CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.ALIPAY_WALLET_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(alipayFeerate)
                ), CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.JINGDONG_PURCHASE_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(jdFeerate)
                ), CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.QQ_PURCHASE_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(qqFeerate)
                ), CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.LAKALA_WALLET_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(lklWalletFeeRate)
                ), CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.UNIONPAY_WALLET_DEBIT_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(unionFeeRate)
                ), CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.UNIONPAY_WALLET_CREDIT_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(unionFeeRate)
                ), CollectionUtil.hashMap(LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.BESTPAY_PURCHASE_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, formatFeerate(bestPayFeeRate)
                ));
        return list;
    }

    /**
     * 创建终端绑定
     *
     * @param merchantSn
     * @param provider
     */
    public void doCreateProviderTerminal(String merchantSn, Integer provider) {
        //商户下门店
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (WosaiMapUtils.isEmpty(merchant)) {
            log.info("商户号 {} 商户不存在", merchantSn);
            return;
        }
        String merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);
        final List<Map> storeList = storeBiz.getStoreListByMerchantId(merchantId);
        //绑定门店级别终端
        if (!CollectionUtils.isEmpty(storeList)) {
            log.info("商户号 {} 开始绑定门店,门店数据:{}", merchantSn, JSONObject.toJSONString(storeList));
            storeList.stream().forEach(store -> handleSqbStoreTerminal(BeanUtil.getPropString(store, Store.SN), merchantSn, provider));
        }
        //商户下所有绑定的终端
        Map filter = CollectionUtil.hashMap(Terminal.STATUS, Terminal.STATUS_ACTIVATED);
        ListResult terminals = terminalService.getTerminals(merchantId, null, null, filter);

        List<String> terminalLevel = Lists.newArrayList(applicationApolloConfig.getTerminalLevelVendorAppAppid());
        final List<Map> terminalList = Optional.ofNullable(terminals.getRecords()).orElseGet(ArrayList::new).parallelStream()
                .filter(ter -> terminalLevel.contains(BeanUtil.getPropString(ter, Terminal.VENDOR_APP_APPID)))
                .filter(Objects::nonNull).collect(Collectors.toList());
        //终端绑定
        if (!CollectionUtils.isEmpty(terminalList)) {
            log.info("商户号:{}开始绑定终端,终端数据:{}", merchantSn, JSONObject.toJSONString(terminalList));
            MerchantInfo merchantInfo = new MyObjectMapper().convertValue(merchant, MerchantInfo.class);
            terminalList.forEach(terminal -> handleSqbTerminalBind(merchantInfo, terminal, provider));
        }
    }

    /**
     * 拉卡拉的费率表示为千分位
     * 0.38 ---> 0.0038
     * 0.6  ---> 0.006 这么转换
     *
     * @param feerate
     * @return
     */
    protected String formatFeerate(String feerate) {
        String str = String.format("%.4f", Double.valueOf(feerate) / 100);
        char lastChar = str.charAt(str.length() - 1);
        if (lastChar == '0') {
            str = str.substring(0, str.length() - 1);
        }
        return str;
    }

    protected String getLadderFee(Map merchantConfig, String feeKey) {
        if (isLadderFeeRate(merchantConfig)) {
            Object configs = BeanUtil.getProperty(merchantConfig, MerchantConfig.LADDER_FEE_RATES);
            if (configs != null && configs instanceof List) {
                String fee = null;
                for (Map config : (List<Map>) configs) {
                    if (fee == null || Double.valueOf(BeanUtil.getPropString(config, feeKey, "0")).doubleValue() > Double.valueOf(fee).doubleValue()) {
                        fee = BeanUtil.getPropString(config, feeKey);
                    }
                }
                if (null != fee) {
                    return fee;
                }
            }
        }
        return BeanUtil.getPropString(merchantConfig, feeKey);
    }

    //渠道阶梯费率
    protected static final String CREDIT_CHANNEL_LADDER = "credit_channel_ladder";
    protected static final String DEBIT_CHANNEL_LADDER = "debit_channel_ladder";
    //渠道费率
    protected static final String CREDIT_CHANNEL = "credit_channel";
    protected static final String DEBIT_CHANNEL = "debit_channel";
    //普通费率  阶梯/非阶梯
    protected static final String LADDER = "ladder";
    protected static final String FEE = "fee";

    //添加费率类型
    protected static final String TYPE_WEIXIN = "weixin";

    protected FeeData getLadderFeeV2(Map merchantConfig, String feeKey) {
        log.info("添加微信费率,config : {}", JSON.toJSONString(merchantConfig));
        FeeData feeData = new FeeData();
        Map params = (Map) merchantConfig.get(MerchantConfig.PARAMS);
        if (WosaiMapUtils.isNotEmpty(params)) {
            String feeType = MapUtils.getString(params, MerchantConfig.FEE_RATE_TYPE);

            //渠道
            if (WosaiStringUtils.equals(feeType, MerchantConfig.FEE_RATE_TYPE_CHANNEL) || BeanUtil.getPropInt(merchantConfig, "params.channel_status", MerchantConfig.STATUS_CLOSED) == MerchantConfig.STATUS_OPENED) {
                /* bankcard_fee :
                 {  贷记卡 ↓
                    "credit": {
                        "fee": "0.5",
                        "max": 1102
                    },借记卡 ↓
                    "debit": {
                        "fee": "0.5",
                        "max": 1102
                    }
                }
                */
                Map bankcard_fee = (Map) params.get(TransactionParam.PARAMS_BANKCARD_FEE);
                feeData.setCredit_channel(BeanUtil.getPropString(bankcard_fee, "credit.fee"))
                        .setDebit_channel(BeanUtil.getPropString(bankcard_fee, "others.fee"));

                //渠道阶梯
            } else if (WosaiStringUtils.equals(feeType, MerchantConfig.FEE_RATE_TYPE_CHANNEL_LADDER)) {

                Map channel_ladder_fee_rate = (Map) params.get(TransactionParam.CHANNEL_LADDER_FEE_RATES);
                if (WosaiMapUtils.isNotEmpty(channel_ladder_fee_rate)) {
                    if (channel_ladder_fee_rate.get(TransactionParam.CHANNEL_LADDER_FEE_RATES_CREDIT) != null && channel_ladder_fee_rate.get(TransactionParam.CHANNEL_LADDER_FEE_RATES_CREDIT) instanceof List) {
                        List<Map> credit = (List) channel_ladder_fee_rate.get(TransactionParam.CHANNEL_LADDER_FEE_RATES_CREDIT);
                        String creditFee = getChannelFee(credit, feeKey);
                        feeData.setCredit_channel_ladder(creditFee);

                    }
                    if (channel_ladder_fee_rate.get(TransactionParam.CHANNEL_LADDER_FEE_RATES_OTHERS) != null && channel_ladder_fee_rate.get(TransactionParam.CHANNEL_LADDER_FEE_RATES_OTHERS) instanceof List) {
                        List<Map> others = (List) channel_ladder_fee_rate.get(TransactionParam.CHANNEL_LADDER_FEE_RATES_OTHERS);
                        String othersFee = getChannelFee(others, feeKey);
                        feeData.setDebit_channel_ladder(othersFee);
                    }
                }

            }
        }
        if (isLadderFeeRate(merchantConfig)) {
            Object configs = BeanUtil.getProperty(merchantConfig, MerchantConfig.LADDER_FEE_RATES);
            if (configs != null && configs instanceof List) {
                String fee = null;
                for (Map config : (List<Map>) configs) {
                    if (fee == null || Double.valueOf(BeanUtil.getPropString(config, feeKey, "0")).doubleValue() > Double.valueOf(fee).doubleValue()) {
                        fee = BeanUtil.getPropString(config, feeKey);
                    }
                }
                feeData.setLadder(fee);
            }
        }
        String defaultFee = BeanUtil.getPropString(merchantConfig, feeKey);
        feeData.setFee(defaultFee);
        return feeData;
    }

    private String getChannelFee(List<Map> configs, String feeKey) {
        String fee = null;
        for (Map config : configs) {
            if (fee == null || Double.parseDouble(BeanUtil.getPropString(config, feeKey, "0")) > Double.parseDouble(fee)) {
                fee = BeanUtil.getPropString(config, feeKey);
            }
        }
        return fee;
    }

    protected boolean isLadderFeeRate(Map feeRateConfig) {
        return BeanUtil.getPropInt(feeRateConfig, MerchantConfig.LADDER_STATUS) == MerchantConfig.STATUS_OPENED ||
                MerchantConfig.FEE_RATE_TYPE_LADDER.equals(BeanUtil.getPropString(feeRateConfig, MerchantConfig.FEE_RATE_TYPE));
    }

    protected void handleFee(List list, FeeData feeData, String type) {
        log.info("添加微信费率,list: {} , data: {}", list, feeData);
        String fee = WosaiStringUtils.isNotEmpty(feeData.getLadder()) ? feeData.getLadder() : feeData.getFee();
        //渠道阶梯
        if (isChannelLadder(feeData)) {
            switch (type) {
                case TYPE_WEIXIN:
                    list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "339", LakalaConstant.FEERATE_PCT, feeData.getCredit_channel_ladder()));
                    list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "338", LakalaConstant.FEERATE_PCT, feeData.getDebit_channel_ladder()));
                    break;
            }


        } else if (isChannel(feeData)) {
            //渠道
            switch (type) {
                case TYPE_WEIXIN:
                    list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "339", LakalaConstant.FEERATE_PCT, feeData.getCredit_channel()));
                    list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "338", LakalaConstant.FEERATE_PCT, feeData.getDebit_channel()));
                    break;
            }
        } else {
            //普通
            switch (type) {
                case TYPE_WEIXIN:
                    list.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, LakalaConstant.FEERATE_CODE_WECHAT, LakalaConstant.FEERATE_PCT, fee));
                    break;
            }
        }

    }

    //是否是渠道阶梯
    private boolean isChannelLadder(FeeData feeData) {
        return WosaiStringUtils.isNotEmpty(feeData.getCredit_channel_ladder()) && WosaiStringUtils.isNotEmpty(feeData.getDebit_channel_ladder());
    }

    //是否是渠道
    private boolean isChannel(FeeData feeData) {
        return WosaiStringUtils.isNotEmpty(feeData.getCredit_channel()) && WosaiStringUtils.isNotEmpty(feeData.getDebit_channel());
    }

    protected boolean rangeInDefined(Double current, Double min, Double max) {
        return Math.max(min, current) == Math.min(current, max);
    }

    protected boolean isSubBiz(String merchantSn, String acquire) {
        return subBizParamsBiz.isInSubBiz(merchantSn, acquire);
    }

    protected boolean isUpdateLicenseNeedUpdateAccount(Map<String, Object> contextMap) {
        try {
            if (org.apache.commons.collections4.MapUtils.isEmpty(contextMap) || !contextMap.containsKey(LICENSE_UPDATE_V2_NEED_UPDATE_ACCOUNT_KEY)) {
                return false;
            }
            return MapUtils.getBoolean(contextMap, LICENSE_UPDATE_V2_NEED_UPDATE_ACCOUNT_KEY);
        } catch (Exception e) {
            return false;
        }
    }

}
