package com.wosai.upay.job.refactor.task.rotational.entity;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 轮询子任务类型枚举
 *
 * <AUTHOR>
 */
public enum RotationalSubTaskTypeEnum implements ITextValueEnum<String> {

    FU_YOU_LICENSE_MANUAL_AUDIT("富友营业执照人工审核", "富友营业执照人工审核"),

    DISABLED_MERCHANT_GET_CHANGE_CARD_RESULT("小微升级禁用的商户获取改卡结果", "小微升级禁用的商户获取改卡结果"),

    ACQUIRER_ACCOUNT_CHANGE_POLLING("收单机构换卡结果轮询", "收单机构换卡结果轮询");

    private final String value;
    private final String text;

    RotationalSubTaskTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public String getValue() {
        return this.value;
    }
}
