package com.wosai.upay.job.refactor.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.wosai.upay.job.refactor.mapper.GroupRouteRuleDetailMapper;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRuleDetailDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 进件规则详情表表数据库访问层 {@link GroupRouteRuleDetailDO}
 * 对McRuleDetailMapper层做出简单封装 {@link GroupRouteRuleDetailMapper}
 *
 * <AUTHOR>
 */
@Repository
public class GroupRouteRuleDetailDAO extends AbstractBaseDAO<GroupRouteRuleDetailDO, GroupRouteRuleDetailMapper> {

    @Autowired
    public GroupRouteRuleDetailDAO(SqlSessionFactory sqlSessionFactory, GroupRouteRuleDetailMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 获取所有的实体对象
     *
     * @return 实体对象列表
     */
    @Override
    public List<GroupRouteRuleDetailDO> listAll() {
        return entityMapper.selectList(new LambdaQueryWrapper<GroupRouteRuleDetailDO>().last("limit 5000"));
    }

    /**
     * 获取所有有效的的进件规则详情
     *
     * @return 进件规则详情列表
     */
    public List<GroupRouteRuleDetailDO> listAllValid() {
        return entityMapper.selectList(new LambdaQueryWrapper<GroupRouteRuleDetailDO>().eq(GroupRouteRuleDetailDO::getValidStatus, ValidStatusEnum.VALID.getValue()));
    }

    /**
     * 根据规则决策id查询进件规则详情
     *
     * @param ruleDecisionId 规则决策id
     * @return 进件规则详情列表
     */
    public List<GroupRouteRuleDetailDO> listByRuleDecisionId(Long ruleDecisionId) {
        LambdaQueryWrapper<GroupRouteRuleDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GroupRouteRuleDetailDO::getRuleDecisionId, ruleDecisionId).eq(GroupRouteRuleDetailDO::getValidStatus, ValidStatusEnum.VALID.getValue());
        return entityMapper.selectList(queryWrapper);
    }


    /**
     * 根据规则决策id批量删除
     *
     * @param ruleDecisionIds 规则决策id列表
     * @return 删除条数
     */
    public Integer batchDeleteByRuleDecisionIds(Collection<Long> ruleDecisionIds) {
        if (CollectionUtils.isEmpty(ruleDecisionIds)) {
            return 0;
        }
        return entityMapper.delete(new LambdaQueryWrapper<GroupRouteRuleDetailDO>().in(GroupRouteRuleDetailDO::getRuleDecisionId, ruleDecisionIds));
    }

    /**
     * 根据id更新有效状态
     *
     * @param ids   id集合
     * @param status 状态
     */
    public void updateValidStatusByIds(Set<Long> ids, Integer status) {
        if (CollUtil.isEmpty(ids) || Objects.isNull(status)) {
            return;
        }
        LambdaQueryWrapper<GroupRouteRuleDetailDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .in(GroupRouteRuleDetailDO::getId, ids);
        GroupRouteRuleDetailDO detailDO = new GroupRouteRuleDetailDO();
        detailDO.setValidStatus(status);
        entityMapper.update(detailDO, lambdaQueryWrapper);
    }

    /**
     * 根据id列表查询
     *
     * @param ids id列表
     * @return 进件路由规则决策
     */
    public List<GroupRouteRuleDetailDO> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<GroupRouteRuleDetailDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .in(GroupRouteRuleDetailDO::getId, ids);
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 批量更新或者插入,id已存在更新,id不存在插入
     *
     * @param groupRouteRuleDetailDOS 进件路由规则决策详情
     * @return effect rows
     */
    public Integer batchInsertOrUpdateById(List<GroupRouteRuleDetailDO> groupRouteRuleDetailDOS) {
        Integer effectRows = 0;
        if (CollectionUtils.isEmpty(groupRouteRuleDetailDOS)) {
            return 0;
        }
        Set<Long> existedIdSets = listByIds(groupRouteRuleDetailDOS.stream().map(GroupRouteRuleDetailDO::getId)
                .collect(Collectors.toList())).stream().map(GroupRouteRuleDetailDO::getId).collect(Collectors.toSet());
        Map<Boolean, List<GroupRouteRuleDetailDO>> dataMap = groupRouteRuleDetailDOS.stream().collect(Collectors.partitioningBy(t -> existedIdSets.contains(t.getId())));
        List<GroupRouteRuleDetailDO> updateList = dataMap.get(Boolean.TRUE);
        List<GroupRouteRuleDetailDO> insertList = dataMap.get(Boolean.FALSE);
        effectRows += batchOperation(updateList, GroupRouteRuleDetailMapper::updateById);
        effectRows += batchOperation(insertList, GroupRouteRuleDetailMapper::insert);
        return effectRows;
    }
}
