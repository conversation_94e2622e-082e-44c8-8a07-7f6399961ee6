package com.wosai.upay.job.refactor.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 收单机构换卡记录表
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@TableName("acquirer_account_change_record")
@Accessors(chain = true)
public class AcquirerAccountChangeRecordDO {
    
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    
    /**
     * 收单机构
     */
    @TableField(value = "acquirer")
    private String acquirer;
    
    /**
     * 收单机构商户号
     */
    @TableField(value = "acquirer_merchant_id")
    private String acquirerMerchantId;
    
    /**
     * 原银行卡信息(JSON)
     */
    @TableField(value = "old_account_info")
    private String oldAccountInfo;
    
    /**
     * 新银行卡信息(JSON)
     */
    @TableField(value = "new_account_info")
    private String newAccountInfo;
    
    /**
     * 状态：0处理中 1成功 2失败
     */
    @TableField(value = "status")
    private Integer status;
    
    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;
    
    /**
     * 额外信息
     */
    @TableField(value = "extra")
    private String extra;
    
    /**
     * 创建时间
     */
    @TableField(value = "ctime")
    private Date ctime;
    
    /**
     * 更新时间
     */
    @TableField(value = "mtime")
    private Date mtime;
    
    /**
     * 完成时间
     */
    @TableField(value = "complete_time")
    private Date completeTime;
}