package com.wosai.upay.job.refactor.task.rotational;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.wosai.upay.job.dto.BankAccountSimpleInfoBO;
import com.wosai.upay.job.model.dto.AcquirerAccountChangeRecordRequest;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.enums.AcquirerAccountChangeStatusEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleSubTaskStatusEnum;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalSubTaskTypeEnum;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalTaskContext;
import com.wosai.upay.job.service.AcquirerAccountChangeValidationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 收单机构换卡结果轮询任务处理器
 * 
 * 功能：
 * 1. 轮询contract_sub_task的处理结果
 * 2. 成功时记录换卡信息到acquirer_account_change_record表
 * 3. 支持多种收单机构的换卡结果查询
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Component
@Slf4j
public class AcquirerAccountChangePollingSubTaskProcessor implements RotationalSubTaskProcessor {

    /**
     * contract_sub_task表的主键ID
     */
    public static final String CONTRACT_SUB_TASK_ID_KEY = "contractSubTaskId";
    
    /**
     * 收单机构类型
     */
    public static final String ACQUIRER_KEY = "acquirer";
    
    /**
     * 收单机构商户ID
     */
    public static final String ACQUIRER_MERCHANT_ID_KEY = "acquirerMerchantId";
    
    /**
     * 原卡信息（对象或JSON字符串）
     * 支持：Map / BankAccountCheckInfo / String(JSON)
     */
    public static final String OLD_ACCOUNT_INFO_KEY = "oldAccountInfo";
    
    /**
     * 新卡信息（对象或JSON字符串）
     * 支持：Map / BankAccountCheckInfo / String(JSON)
     */
    public static final String NEW_ACCOUNT_INFO_KEY = "newAccountInfo";
    
    /**
     * 备注信息
     */
    public static final String REMARK_KEY = "remark";

    @Resource
    private ContractSubTaskDAO contractSubTaskDAO;
    
    @Resource
    private AcquirerFacade acquirerFacade;
    
    @Resource
    private AcquirerAccountChangeValidationService accountChangeValidationService;

    @Override
    public RotationalSubTaskTypeEnum getSubTaskType() {
        return RotationalSubTaskTypeEnum.ACQUIRER_ACCOUNT_CHANGE_POLLING;
    }

    @Override
    public InternalScheduleSubTaskProcessResultBO handleRotationalSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        String merchantSn = mainTaskDO.getMerchantSn();
        RotationalTaskContext rotationalTaskContext = JSON.parseObject(mainTaskDO.getContext(), RotationalTaskContext.class);
        
        try {
            TaskParams taskParams = extractTaskParams(rotationalTaskContext);
            if (!taskParams.isValid()) {
                return InternalScheduleSubTaskProcessResultBO.fail("任务参数不完整：" + taskParams.getValidationError());
            }
            
            ContractSubTaskDO contractSubTask = contractSubTaskDAO.getByPrimaryKey(taskParams.contractSubTaskId).orElse(null);
            if (Objects.isNull(contractSubTask)) {
                log.warn("ContractSubTask not found, contractSubTaskId={}", taskParams.contractSubTaskId);
                return InternalScheduleSubTaskProcessResultBO.fail("找不到对应的contract_sub_task记录");
            }
            
            return processContractSubTaskStatus(merchantSn, contractSubTask, taskParams);
            
        } catch (Exception e) {
            log.error("Handle account change polling task failed, merchantSn={}, mainTaskId={}, subTaskId={}", 
                    merchantSn, mainTaskDO.getId(), subTaskDO.getId(), e);
            return InternalScheduleSubTaskProcessResultBO.fail("处理换卡轮询任务失败：" + e.getMessage());
        }
    }

    /**
     * 提取任务参数
     */
    private TaskParams extractTaskParams(RotationalTaskContext context) {
        TaskParams params = new TaskParams();
        
        try {
            params.contractSubTaskId = MapUtils.getLong(context.getParamContext(), CONTRACT_SUB_TASK_ID_KEY);
            params.acquirer = MapUtils.getString(context.getParamContext(), ACQUIRER_KEY);
            params.acquirerMerchantId = MapUtils.getString(context.getParamContext(), ACQUIRER_MERCHANT_ID_KEY);
            Object oldVal = context.getParamContext().get(OLD_ACCOUNT_INFO_KEY);
            Object newVal = context.getParamContext().get(NEW_ACCOUNT_INFO_KEY);
            params.oldAccountInfo = normalizeAccountInfoToMap(oldVal);
            params.newAccountInfo = normalizeAccountInfoToMap(newVal);
            params.remark = MapUtils.getString(context.getParamContext(), REMARK_KEY, "");
        } catch (Exception e) {
            log.error("Extract task params failed", e);
            params.validationError = "参数解析失败：" + e.getMessage();
        }
        
        return params;
    }

    /**
     * 处理contract_sub_task状态
     */
    private InternalScheduleSubTaskProcessResultBO processContractSubTaskStatus(
            String merchantSn, ContractSubTaskDO contractSubTask, TaskParams taskParams) {
        
        Optional<ContractSubTaskProcessStatusEnum> statusOpt = EnumUtils.ofNullable(ContractSubTaskProcessStatusEnum.class, contractSubTask.getStatus());
        if (!statusOpt.isPresent()) {
            return  InternalScheduleSubTaskProcessResultBO.fail("contract_sub_task未知状态: " + contractSubTask.getStatus());
        }
        ContractSubTaskProcessStatusEnum status = statusOpt.get();
        switch (status) {
            case PROCESS_SUCCESS:
                return handleSuccessStatus(merchantSn, contractSubTask, taskParams);
                
            case PROCESS_FAIL:
            case SYSTEM_EXCEPTION_FAILURE:
                return handleFailedStatus(contractSubTask);
                
            default:
                return handleProcessingStatus();
        }
    }

    /**
     * 处理成功状态
     */
    private InternalScheduleSubTaskProcessResultBO handleSuccessStatus(
            String merchantSn, ContractSubTaskDO contractSubTask, TaskParams taskParams) {
        
        try {
            // 记录换卡成功信息到acquirer_account_change_record表
            AcquirerAccountChangeRecordRequest recordRequest = buildAccountChangeRecord(
                    merchantSn, contractSubTask, taskParams);
            
            Long recordId = accountChangeValidationService.recordAccountChange(recordRequest);
            
            log.info("Account change record created successfully, merchantSn={}, contractSubTaskId={}, recordId={}", 
                    merchantSn, contractSubTask.getId(), recordId);
            
            InternalScheduleSubTaskProcessResultBO result = new InternalScheduleSubTaskProcessResultBO();
            result.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS);
            result.setResult("换卡成功，已记录换卡信息，recordId=" + recordId);
            result.setRequestMsg(JSON.toJSONString(recordRequest));
            result.setResponseMsg("SUCCESS");
            
            return result;
            
        } catch (Exception e) {
            log.error("Record account change failed, merchantSn={}, contractSubTaskId={}", 
                    merchantSn, contractSubTask.getId(), e);
            
            InternalScheduleSubTaskProcessResultBO result = new InternalScheduleSubTaskProcessResultBO();
            result.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL);
            result.setResult("换卡成功但记录失败：" + e.getMessage());
            
            return result;
        }
    }

    /**
     * 处理失败状态
     */
    private InternalScheduleSubTaskProcessResultBO handleFailedStatus(ContractSubTaskDO contractSubTask) {
        InternalScheduleSubTaskProcessResultBO result = new InternalScheduleSubTaskProcessResultBO();
        result.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL);
        result.setResult("contract_sub_task执行失败：" + contractSubTask.getResult());
        result.setResponseMsg(contractSubTask.getResponseBody());
        return result;
    }

    /**
     * 处理处理中状态
     */
    private InternalScheduleSubTaskProcessResultBO handleProcessingStatus() {
        InternalScheduleSubTaskProcessResultBO result = new InternalScheduleSubTaskProcessResultBO();
        result.setStatus(InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT);
        result.setResult("等待contract_sub_task处理完成");
        return result;
    }

    /**
     * 构建账户变更记录请求
     */
    private AcquirerAccountChangeRecordRequest buildAccountChangeRecord(
            String merchantSn, ContractSubTaskDO contractSubTask, TaskParams taskParams) {
        
        AcquirerAccountChangeRecordRequest request = new AcquirerAccountChangeRecordRequest();
        request.setMerchantSn(merchantSn);
        request.setAcquirer(taskParams.acquirer);
        request.setAcquirerMerchantId(taskParams.acquirerMerchantId);
        request.setOldAccountInfo(convertMapToBankAccountInfo(taskParams.oldAccountInfo));
        request.setNewAccountInfo(convertMapToBankAccountInfo(taskParams.newAccountInfo));
        request.setStatus(AcquirerAccountChangeStatusEnum.SUCCESS.getValue());
        return request;
    }

    /**
     * 解析账户信息JSON为Map
     */
    private Map<String, Object> parseAccountInfoMap(String accountInfoJson) {
        if (StringUtils.isBlank(accountInfoJson)) {
            return null;
        }
        
        try {
            return JSON.parseObject(accountInfoJson, Map.class);
        } catch (Exception e) {
            log.error("Parse account info to map failed, json={}", accountInfoJson, e);
            return null;
        }
    }

    /**
     * 归一化账户信息为 Map，兼容 Map/DTO/String 三种类型
     */
    private Map<String, Object> normalizeAccountInfoToMap(Object val) {
        if (val == null) {
            return null;
        }
        try {
            if (val instanceof Map) {
                //noinspection unchecked
                return (Map<String, Object>) val;
            }
            if (val instanceof BankAccountSimpleInfoBO) {
                BankAccountSimpleInfoBO info = (BankAccountSimpleInfoBO) val;
                Map<String, Object> map = new java.util.HashMap<>();
                map.put("cardNumber", info.getCardNumber());
                map.put("type", info.getType());
                map.put("holder", info.getHolder());
                return map;
            }
            if (val instanceof String) {
                return parseAccountInfoMap((String) val);
            }
            log.warn("Unsupported accountInfo value type: {}", val.getClass());
            return null;
        } catch (Exception e) {
            log.error("Normalize account info to map failed, value={}", val, e);
            return null;
        }
    }

    /**
     * 将Map转换为BankAccountCheckInfo对象
     */
    private BankAccountSimpleInfoBO convertMapToBankAccountInfo(Map<String, Object> accountInfoMap) {
        if (accountInfoMap == null || accountInfoMap.isEmpty()) {
            return null;
        }
        try {
            return JSON.parseObject(JSON.toJSONString(accountInfoMap), BankAccountSimpleInfoBO.class);
        } catch (Exception e) {
            log.error("Convert map to BankAccountCheckInfo failed, map={}", accountInfoMap, e);
            return null;
        }
    }

    /**
     * 解析账户信息JSON（向后兼容方法）
     */
    private BankAccountSimpleInfoBO parseAccountInfo(String accountInfoJson) {
        if (StringUtils.isBlank(accountInfoJson)) {
            return null;
        }
        
        try {
            return JSON.parseObject(accountInfoJson, BankAccountSimpleInfoBO.class);
        } catch (Exception e) {
            log.error("Parse account info failed, json={}", accountInfoJson, e);
            return null;
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 任务参数封装类
     */
    private static class TaskParams {
        Long contractSubTaskId;
        String acquirer;
        String acquirerMerchantId;
        Map<String, Object> oldAccountInfo;
        Map<String, Object> newAccountInfo;
        String remark;
        String validationError;
        
        boolean isValid() {
            if (Objects.isNull(contractSubTaskId)) {
                validationError = "contractSubTaskId不能为空";
                return false;
            }
            
            if (StringUtils.isBlank(acquirer)) {
                validationError = "acquirer不能为空";
                return false;
            }
            
            // oldAccountInfo和newAccountInfo为可选参数，由调用方决定是否填充
            
            return true;
        }
        
        String getValidationError() {
            return validationError;
        }
    }
}
