package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.refactor.mapper.AcquirerAccountChangeRuleMapper;
import com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRuleDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 收单机构账户变更规则DAO
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Repository
@Slf4j
public class AcquirerAccountChangeRuleDAO extends AbstractBaseDAO<AcquirerAccountChangeRuleDO, AcquirerAccountChangeRuleMapper> {

    /**
     * 单个规则缓存
     */
    private static final Cache<String, AcquirerAccountChangeRuleDO> SINGLE_RULE_CACHE = CacheBuilder.newBuilder()
            .maximumSize(200)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .recordStats()
            .build();

    public AcquirerAccountChangeRuleDAO(SqlSessionFactory sqlSessionFactory, AcquirerAccountChangeRuleMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据收单机构查询规则
     */
    public Optional<AcquirerAccountChangeRuleDO> getByAcquirer(String acquirer) {
        if (StringUtils.isEmpty(acquirer)) {
            throw new ContractBizException("收单机构不可以为空");
        }
        try {
            String cacheKey = "single_" + acquirer;
            AcquirerAccountChangeRuleDO cachedRule = SINGLE_RULE_CACHE.getIfPresent(cacheKey);
            if (cachedRule == null) {
                LambdaQueryWrapper<AcquirerAccountChangeRuleDO> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AcquirerAccountChangeRuleDO::getAcquirer, acquirer)
                       .orderByDesc(AcquirerAccountChangeRuleDO::getId);
                Optional<AcquirerAccountChangeRuleDO> result = selectOne(wrapper);
                cachedRule = result.orElse(null);
                if (Objects.nonNull(cachedRule)) {
                    SINGLE_RULE_CACHE.put(cacheKey, cachedRule);
                }
            }
            return Optional.ofNullable(cachedRule);
        } catch (Exception e) {
            log.error("getByAcquirer cache error, acquirer: {}", acquirer, e);
            LambdaQueryWrapper<AcquirerAccountChangeRuleDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AcquirerAccountChangeRuleDO::getAcquirer, acquirer)
                   .orderByDesc(AcquirerAccountChangeRuleDO::getId);
            return selectOne(wrapper);
        }
    }


    /**
     * 清空指定收单机构的缓存
     * 
     * @param acquirer 收单机构
     */
    public void evictCache(String acquirer) {
        if (WosaiStringUtils.isEmpty(acquirer)) {
            return;
        }
        
        SINGLE_RULE_CACHE.invalidate("single_" + acquirer);
        log.info("Evicted cache for acquirer: {}", acquirer);
    }

    /**
     * 清空所有缓存
     * 
     * 在规则批量更新时使用
     */
    public void evictAllCache() {
        SINGLE_RULE_CACHE.invalidateAll();
        log.info("Evicted all rules cache");
    }





}