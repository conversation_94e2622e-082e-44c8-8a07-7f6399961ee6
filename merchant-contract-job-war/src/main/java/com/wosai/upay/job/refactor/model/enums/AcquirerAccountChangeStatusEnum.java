package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 收单机构账户变更状态枚举
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public enum AcquirerAccountChangeStatusEnum implements ITextValueEnum<Integer> {

    /**
     * 处理中
     */
    PROCESSING(0, "处理中"),

    /**
     * 成功
     */
    SUCCESS(1, "成功"),

    /**
     * 失败
     */
    FAILED(2, "失败");

    private final Integer value;
    private final String text;

    AcquirerAccountChangeStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}