package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 内部调度任务类型枚举
 *
 * <AUTHOR>
 */
public enum InternalScheduleTaskTypeEnum implements ITextValueEnum<Integer> {

    BANK_EXCEPTION_CHANGE_TO_THIRD_PARTY(1, "银行通道异常自动切三方收单机构任务"),

    BANK_RECOVER_CHANGE_TO_ORIGINAL(2, "银行通道恢复三方自动切回银行"),

    BUSINESS_LICENCE_CERTIFICATION(3, "营业执照认证"),

    WX_COMPLAINT_RESOLUTION(4, "微信投诉处理解决"),

    TRACK_CONTRACT_RESULT(5, "追踪进件任务结果"),

    BNS_INACTIVE_MERCHANT_CLEAN(6, "bns不活跃商户清理"),

    BUSINESS_LICENCE_CERTIFICATION_V2(7, "营业执照认证V2"),

    ROTATIONAL_TASK(8, "轮询任务"),

    HAIKE_UNION_PAY_CONTRACT_RESULT(10, "海科云闪付报备结果");

    private final Integer value;
    private final String text;

    InternalScheduleTaskTypeEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
