package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 逻辑操作符枚举
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public enum LogicOperatorEnum implements ITextValueEnum<String> {

    /**
     * 且
     */
    AND("and", "且"),

    /**
     * 或
     */
    OR("or", "或");

    private final String value;
    private final String text;

    LogicOperatorEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public String getValue() {
        return this.value;
    }
}