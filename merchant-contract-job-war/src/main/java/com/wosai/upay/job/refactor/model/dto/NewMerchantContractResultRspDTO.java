package com.wosai.upay.job.refactor.model.dto;

import lombok.Data;

import java.util.Map;

/**
 * 新增商户入网接口结果返回DTO
 * todo 待重构 （接口功能定义及类型区分）
 *
 * <AUTHOR>
 * @date 2024/9/10 16:22
 */
@Data
public class NewMerchantContractResultRspDTO {

    public static final Integer STATUS_SUCCESS = 1;

    public static final Integer STATUS_FAIL = 2;

    public static final Integer STATUS_PROCESSING = 3;

    private Map<String, Object> request;

    private Map<String, Object> response;

    private String acquirerMerchantId;

    private String unionMerchantId;

    private String termId;

    private String contractId;

    private String message;


    /**
     * 请求成功（需要异步回调得到请求结果，例如拉卡拉） 进件成功  进件失败
     */
    private Integer status;

    public boolean isProcessing() {
        return STATUS_PROCESSING.equals(status);
    }

    public boolean isSuccess() {
        return STATUS_SUCCESS.equals(status);
    }

    public boolean isFail() {
        return STATUS_FAIL.equals(status);
    }




}
