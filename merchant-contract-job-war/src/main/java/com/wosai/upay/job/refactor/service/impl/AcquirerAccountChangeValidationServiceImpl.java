package com.wosai.upay.job.refactor.service.impl;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.job.model.dto.AcquirerAccountChangeRecordRequest;
import com.wosai.upay.job.refactor.dao.AcquirerAccountChangeRecordDAO;
import com.wosai.upay.job.refactor.dao.AcquirerAccountChangeRuleDAO;
import com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRecordDO;
import com.wosai.upay.job.refactor.model.entity.AcquirerAccountChangeRuleDO;
import com.wosai.upay.job.refactor.model.enums.AcquirerAccountChangeStatusEnum;
import com.wosai.upay.job.refactor.biz.rule.AcquirerAccountChangeRuleEngine;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.utils.object.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import com.wosai.upay.job.dto.AccountChangeValidationResult;
import com.wosai.upay.job.dto.BankAccountSimpleInfoBO;
import com.wosai.upay.job.service.AcquirerAccountChangeValidationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

/**
 * 收单机构账户变更校验服务实现
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Service
@Slf4j
@AutoJsonRpcServiceImpl
public class AcquirerAccountChangeValidationServiceImpl implements AcquirerAccountChangeValidationService {

    @Autowired
    private AcquirerAccountChangeRecordDAO accountChangeRecordDAO;

    @Autowired
    private AcquirerAccountChangeRuleDAO accountChangeRuleDAO;

    @Autowired
    private AcquirerAccountChangeRuleEngine ruleEngine;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource
    private ApplicationApolloConfig applicationApolloConfig;

    @Override
    public AccountChangeValidationResult validateAccountChange(String merchantSn, String acquirer,
                                                               BankAccountSimpleInfoBO oldAccountInfo, BankAccountSimpleInfoBO newAccountInfo) {
        if (!applicationApolloConfig.getAcquirerChangeCardCheckSwitch()) {
            return AccountChangeValidationResult.allowed("不需要校验").withRuleRestriction(false).withRuleMatched(false).withNextChangeTime(new Date());
        }
        if (isInWhitelist(merchantSn)) {
            return AccountChangeValidationResult.allowed("商户在白名单中").withRuleRestriction(false).withRuleMatched(false).withNextChangeTime(new Date());
        }
        if (Objects.isNull(oldAccountInfo) || Objects.isNull(newAccountInfo)) {
            return AccountChangeValidationResult.allowed("缺失银行卡信息").withRuleRestriction(false).withRuleMatched(false).withNextChangeTime(new Date());
        }
        try {
            AcquirerAccountChangeRuleDO rule = accountChangeRuleDAO.getByAcquirer(acquirer).orElse(null);
            if (Objects.isNull(rule)) {
                return AccountChangeValidationResult.allowed("无换卡限制规则，可以进行变更").withRuleRestriction(false).withRuleMatched(false).withNextChangeTime(new Date());
            }
            String providerMerchantId = resolveProviderMerchantId(merchantSn, acquirer);
            if (StringUtils.isBlank(providerMerchantId)) {
                return AccountChangeValidationResult.allowed("缺失机构商户号").withRuleRestriction(false).withRuleMatched(false).withNextChangeTime(new Date());
            }
            // periodDays=0 语义：不看历史次数，当前规则命中则直接拦截（与 maxCount=0 一致）
            if (rule.getPeriodDays() == 0 || rule.getMaxCount() == 0) {
                return validateZeroLimitRule(merchantSn, acquirer, oldAccountInfo, newAccountInfo, rule);
            }
            return validateCountLimitRule(merchantSn, acquirer, providerMerchantId, oldAccountInfo, newAccountInfo, rule);
        } catch (Exception e) {
            log.error("Validate account change error, merchantSn: {}, acquirer: {}", merchantSn, acquirer, e);
            throw new ContractBizException("账户变更校验失败");
        }
    }


    @Override
    public AccountChangeValidationResult validateAccountChangeWithCurrentAccount(String merchantSn, String acquirer, BankAccountSimpleInfoBO newAccountInfo) {
        try {
            Map merchant = merchantService.getMerchantBySn(merchantSn);
            String merchantId = merchant == null ? null : String.valueOf(merchant.get(DaoConstants.ID));
            BankAccountSimpleInfoBO oldAccountInfo = null;
            if (merchantId != null) {
                Map<?, ?> existed = merchantService.getMerchantBankAccountByMerchantId(merchantId);
                oldAccountInfo = convertMapToBankAccountInfo(existed);
            }
            return validateAccountChange(merchantSn, acquirer, oldAccountInfo, newAccountInfo);
        } catch (Exception e) {
            log.error("Validate account change with current account failed, merchantSn={}, acquirer={}", merchantSn, acquirer, e);
            throw new ContractBizException("账户变更校验失败");
        }
    }


    @Override
    public AccountChangeValidationResult validateAccountChange(String merchantSn, String acquirer, String providerMerchantId,
                                                               BankAccountSimpleInfoBO oldAccountInfo, BankAccountSimpleInfoBO newAccountInfo) {
        if (Objects.isNull(oldAccountInfo) || Objects.isNull(newAccountInfo)) {
            return AccountChangeValidationResult.allowed("缺失银行卡信息").withRuleRestriction(false).withRuleMatched(false).withNextChangeTime(new Date());
        }
        if (StringUtils.isBlank(providerMerchantId)) {
            return AccountChangeValidationResult.allowed("缺失机构商户号").withRuleRestriction(false).withRuleMatched(false).withNextChangeTime(new Date());
        }
        try {
            AcquirerAccountChangeRuleDO rule = accountChangeRuleDAO.getByAcquirer(acquirer).orElse(null);
            if (Objects.isNull(rule)) {
                return AccountChangeValidationResult.allowed("无换卡限制规则，可以进行变更").withRuleRestriction(false).withRuleMatched(false).withNextChangeTime(new Date());
            }
            if (rule.getPeriodDays() == 0 || rule.getMaxCount() == 0) {
                return validateZeroLimitRule(merchantSn, acquirer, oldAccountInfo, newAccountInfo, rule);
            }
            return validateCountLimitRule(merchantSn, acquirer, providerMerchantId, oldAccountInfo, newAccountInfo, rule);
        } catch (Exception e) {
            log.error("Validate account change error, merchantSn: {}, acquirer: {}, providerMerchantId: {}", merchantSn, acquirer, providerMerchantId, e);
            throw new ContractBizException("账户变更校验失败");
        }
    }


    public int countAccountChanges(String merchantSn, String acquirer, String providerMerchantId) {
        try {
            AcquirerAccountChangeRuleDO rule = accountChangeRuleDAO.getByAcquirer(acquirer).orElse(null);
            if (Objects.isNull(rule)) {
                return 0;
            }
            
            if (Objects.isNull(rule.getMaxCount()) || rule.getMaxCount() == 0) {
                log.debug("Count skipped for zero limit rule, merchantSn={}, acquirer={}, providerMerchantId={}, maxCount=0", 
                        merchantSn, acquirer, providerMerchantId);
                return 0;
            }
            
            Date now = new Date();
            Date startDate = addDays(now, -rule.getPeriodDays());
            List<AcquirerAccountChangeRecordDO> records;
            if (StringUtils.isNotBlank(providerMerchantId)) {
                records = accountChangeRecordDAO.findSuccessRecords(merchantSn, acquirer, providerMerchantId, startDate, now);
            } else {
                records = accountChangeRecordDAO.findSuccessRecords(merchantSn, acquirer, startDate, now);
            }

            return Math.toIntExact(records.stream()
                    .filter(record -> shouldCountRecord(record, rule))
                    .count());
                    
        } catch (Exception e) {
            log.error("Count account changes error, merchantSn: {}, acquirer: {}, providerMerchantId: {}", 
                    merchantSn, acquirer, providerMerchantId, e);
            return 0;
        }
    }
    
    public Date calculateNextChangeTime(String merchantSn, String acquirer) {
        // 兼容旧签名，内部仍按不区分机构商户号计算
        return calculateNextChangeTime(merchantSn, acquirer, null);
    }

    /**
     * 计算下次可变更时间（可按机构商户号维度）
     */
    public Date calculateNextChangeTime(String merchantSn, String acquirer, String providerMerchantId) {
        try {
            AcquirerAccountChangeRuleDO rule = accountChangeRuleDAO.getByAcquirer(acquirer).orElse(null);
            if (rule == null) {
                return new Date();
            }
            
            // 当maxCount=0时，下次变更时间的概念不适用
            if (rule.getMaxCount() == 0) {
                log.debug("Next change time calculation skipped for zero limit rule, " +
                        "merchantSn={}, acquirer={}, providerMerchantId={}, maxCount=0", merchantSn, acquirer, providerMerchantId);
                return new Date(); // 零限制模式下，允许与否完全取决于规则匹配，而非时间
            }
            
            Date now = new Date();
            Date startDate = addDays(now, -rule.getPeriodDays());
            List<AcquirerAccountChangeRecordDO> records = (providerMerchantId != null && !providerMerchantId.trim().isEmpty())
                    ? accountChangeRecordDAO.findSuccessRecords(merchantSn, acquirer, providerMerchantId, startDate, now)
                    : accountChangeRecordDAO.findSuccessRecords(merchantSn, acquirer, startDate, now);
            
            List<AcquirerAccountChangeRecordDO> matchedRecords = records.stream()
                    .filter(record -> shouldCountRecord(record, rule))
                    .sorted(Comparator.comparing(AcquirerAccountChangeRecordDO::getCompleteTime))
                    .collect(java.util.stream.Collectors.toList());
            
            int currentCount = matchedRecords.size();
            
            if (matchedRecords.isEmpty()) {
                return addDays(new Date(), rule.getPeriodDays());
            }
            
            if (currentCount < rule.getMaxCount()) {
                Date earliestRecordTime = matchedRecords.get(0).getCompleteTime();
                Date nextChangeTime = addDays(earliestRecordTime, rule.getPeriodDays() + 1);
                if (nextChangeTime.before(now)) {
                    return new Date();
                }

                return nextChangeTime;
            }

            // 找到最后MaxCount条记录中CompleteTime最小的记录
            int startIndex = Math.max(0, currentCount - rule.getMaxCount());
            Date earliestTimeInLastMaxCountRecords = matchedRecords.subList(startIndex, currentCount)
                    .stream()
                    .map(AcquirerAccountChangeRecordDO::getCompleteTime)
                    .min(Date::compareTo)
                    .orElse(matchedRecords.get(startIndex).getCompleteTime());
            
            Date nextChangeTime = addDays(earliestTimeInLastMaxCountRecords, rule.getPeriodDays() + 1);
            if (nextChangeTime.before(now)) {
                return new Date();
            }
            
            return nextChangeTime;
            
        } catch (Exception e) {
            log.error("Calculate next change time error, merchantSn: {}, acquirer: {}, providerMerchantId: {}", 
                    merchantSn, acquirer, providerMerchantId, e);
            return new Date();
        }
    }
    
    @Override
    public Long recordAccountChange(AcquirerAccountChangeRecordRequest request) {
        try {
            AcquirerAccountChangeRecordDO record = new AcquirerAccountChangeRecordDO();
            record.setMerchantSn(request.getMerchantSn());
            record.setAcquirer(request.getAcquirer());
            record.setAcquirerMerchantId(request.getAcquirerMerchantId());
            record.setOldAccountInfo(JSON.toJSONString(request.getOldAccountInfo()));
            record.setNewAccountInfo(JSON.toJSONString(request.getNewAccountInfo()));
            record.setStatus(request.getStatus());
            record.setRemark(request.getRemark());
            record.setCtime(new Date());
            
            if (AcquirerAccountChangeStatusEnum.SUCCESS.getValue().equals(request.getStatus())) {
                record.setCompleteTime(new Date());
            }
            
            accountChangeRecordDAO.insertOne(record);
            return record.getId();
            
        } catch (Exception e) {
            log.error("Record account change error, request: {}", request, e);
            throw new ContractBizException("记录账户变更信息失败");
        }
    }
    
    @Override
    public boolean updateAccountChangeStatus(Long recordId, Integer status, String remark) {
        try {
            AcquirerAccountChangeRecordDO record = accountChangeRecordDAO.getByPrimaryKey(recordId).orElse(null);
            if (record == null) {
                log.warn("Record not found, recordId: {}", recordId);
                return false;
            }

            record.setStatus(status);
            record.setRemark(remark);
            
            if (AcquirerAccountChangeStatusEnum.SUCCESS.getValue().equals(status) || AcquirerAccountChangeStatusEnum.FAILED.getValue().equals(status)) {
                record.setCompleteTime(new Date());
            }
            return accountChangeRecordDAO.updateByPrimaryKeySelective(record) > 0;
        } catch (Exception e) {
            log.error("Update account change status error, recordId: {}, status: {}", 
                    recordId, status, e);
            return false;
        }
    }
    

    
    /**
     * 校验零限制规则（maxCount=0）
     * 语义：如果满足规则条件则完全不允许换卡
     */
    private AccountChangeValidationResult validateZeroLimitRule(String merchantSn, String acquirer,
                                                                BankAccountSimpleInfoBO oldAccountInfo, BankAccountSimpleInfoBO newAccountInfo,
                                                                AcquirerAccountChangeRuleDO rule) {
        
        AcquirerAccountChangeRecordDO testRecord = createTestRecord(merchantSn, acquirer, oldAccountInfo, newAccountInfo);
        boolean matchesRule = ruleEngine.evaluate(testRecord, rule.getRuleLogic());
        
        if (matchesRule) {
            logZeroLimitBlocked(merchantSn, acquirer, rule, oldAccountInfo, newAccountInfo);
            String ruleName = Objects.isNull(rule.getRuleName()) ? "未命名规则" : rule.getRuleName();
            String blockedMessage = String.format("根据规则配置，此类账户变更不被允许（规则：%s）", ruleName);
            return AccountChangeValidationResult.blocked(blockedMessage)
                    .withRuleInfo(rule.getMaxCount(), rule.getPeriodDays())
                    .withUsedCount(0)
                    .withRuleRestriction(true)
                    .withRuleMatched(true);
        } else {
            logZeroLimitAllowed(merchantSn, acquirer, rule, oldAccountInfo, newAccountInfo);
            String ruleName = Objects.isNull(rule.getRuleName()) ? "未命名规则" : rule.getRuleName();
            String allowedMessage = String.format("当前变更不匹配限制规则，允许进行变更（规则：%s）", ruleName);
            return AccountChangeValidationResult.allowed(allowedMessage)
                    .withRuleInfo(rule.getMaxCount(), rule.getPeriodDays())
                    .withUsedCount(0)
                    .withRuleRestriction(true)
                    .withRuleMatched(false)
                    .withNextChangeTime(new Date()); // 零限制规则允许时，立即可以变更
        }
    }
    
    /**
     * 校验次数限制规则（maxCount>0）
     * 语义：在指定周期内限制匹配规则的变更次数
     */
    private AccountChangeValidationResult validateCountLimitRule(String merchantSn, String acquirer,
                                                                 String providerMerchantId,
                                                                 BankAccountSimpleInfoBO oldAccountInfo, BankAccountSimpleInfoBO newAccountInfo,
                                                                 AcquirerAccountChangeRuleDO rule) {
        String ruleName = Objects.isNull(rule.getRuleName()) ? "未命名规则" : rule.getRuleName();

        // 评估本次变更是否匹配规则条件 如果本次变更不匹配规则条件，则允许变更（不受次数限制）
        AcquirerAccountChangeRecordDO testRecord = createTestRecord(merchantSn, acquirer, oldAccountInfo, newAccountInfo);
        boolean matchesRule = ruleEngine.evaluate(testRecord, rule.getRuleLogic());
        
        if (!matchesRule) {
            String message = String.format("当前变更不匹配限制规则，允许进行变更（规则：%s）", ruleName);
            logCountLimitAllowed(merchantSn, acquirer, rule, 0);
            return AccountChangeValidationResult.allowed(message)
                    .withRuleInfo(rule.getMaxCount(), rule.getPeriodDays())
                    .withUsedCount(0)
                    .withRuleRestriction(true)
                    .withRuleMatched(false)
                    .withNextChangeTime(new Date()); // 不匹配规则时，立即可以变更
        }
        
        int currentCount = countAccountChanges(merchantSn, acquirer, providerMerchantId);
        
        if (currentCount >= rule.getMaxCount()) {
            Date nextChangeTime = calculateNextChangeTime(merchantSn, acquirer, providerMerchantId);
            String message = String.format("超过最大变更次数限制，%d天内最多允许变更%d次（规则：%s）",
                    rule.getPeriodDays(), rule.getMaxCount(), ruleName);
            AccountChangeValidationResult result = AccountChangeValidationResult.blocked(message, nextChangeTime)
                    .withRuleInfo(rule.getMaxCount(), rule.getPeriodDays())
                    .withUsedCount(currentCount)
                    .withRuleRestriction(true)
                    .withRuleMatched(true);
            logCountLimitBlocked(merchantSn, acquirer, rule, currentCount, result, oldAccountInfo, newAccountInfo);
            return result;
        }
        
        String message = String.format("允许变更，%d天内还可变更%d次（规则：%s）", rule.getPeriodDays(), rule.getMaxCount() - currentCount, ruleName);
        Date nextChangeTime = calculateNextChangeTime(merchantSn, acquirer, providerMerchantId);
        AccountChangeValidationResult result = AccountChangeValidationResult.allowed(message)
                .withRuleInfo(rule.getMaxCount(), rule.getPeriodDays())
                .withUsedCount(currentCount)
                .withRuleRestriction(true)
                .withRuleMatched(true)
                .withNextChangeTime(nextChangeTime);
        logCountLimitAllowed(merchantSn, acquirer, rule, currentCount);
        return result;
    }

    /**
     * 解析收单机构商户号
     */
    private String resolveProviderMerchantId(String merchantSn, String acquirer) {
        try {
            // 优先通过枚举方式获取
            String providerMerchantId = EnumUtils.ofNullable(AcquirerTypeEnum.class, acquirer)
                    .flatMap(type -> merchantTradeParamsBiz.getMerchantAcquirerMerchantId(merchantSn, type))
                    .orElse(null);
            if (providerMerchantId != null) {
                return providerMerchantId;
            }
            // 兼容兜底：通过acquirer字符串方式获取
            return merchantTradeParamsBiz.getAcquirerMerchantId(merchantSn, acquirer).orElse(null);
        } catch (Exception e) {
            log.warn("Resolve providerMerchantId failed, merchantSn={}, acquirer={}", merchantSn, acquirer, e);
            return null;
        }
    }
    
    /**
     * 创建测试记录用于规则引擎评估
     */
    private AcquirerAccountChangeRecordDO createTestRecord(String merchantSn, String acquirer,
                                                           BankAccountSimpleInfoBO oldAccountInfo, BankAccountSimpleInfoBO newAccountInfo) {
        AcquirerAccountChangeRecordDO testRecord = new AcquirerAccountChangeRecordDO();
        testRecord.setMerchantSn(merchantSn);
        testRecord.setAcquirer(acquirer);
        testRecord.setOldAccountInfo(JSON.toJSONString(oldAccountInfo));
        testRecord.setNewAccountInfo(JSON.toJSONString(newAccountInfo));
        testRecord.setStatus(AcquirerAccountChangeStatusEnum.SUCCESS.getValue());
        testRecord.setCtime(new Date());
        testRecord.setCompleteTime(new Date());
        return testRecord;
    }
    
    /**
     * 记录零限制规则阻止的日志
     */
    private void logZeroLimitBlocked(String merchantSn, String acquirer, AcquirerAccountChangeRuleDO rule,
                                     BankAccountSimpleInfoBO oldAccountInfo, BankAccountSimpleInfoBO newAccountInfo) {
        log.warn("Account change blocked due to zero_limit_rule_matched, " +
                "merchantSn={}, acquirer={}, ruleName={}, " +
                "maxCount=0, ruleMatched=true, " +
                "oldAccountInfo={}, newAccountInfo={}, ruleLogic={}",
                merchantSn, acquirer, rule.getRuleName(),
                formatAccountInfo(oldAccountInfo), formatAccountInfo(newAccountInfo),
                rule.getRuleLogic());
    }
    
    /**
     * 记录零限制规则允许的日志
     */
    private void logZeroLimitAllowed(String merchantSn, String acquirer, AcquirerAccountChangeRuleDO rule,
                                     BankAccountSimpleInfoBO oldAccountInfo, BankAccountSimpleInfoBO newAccountInfo) {
        log.info("Account change validation passed, reason=zero_limit_rule_not_matched, " +
                "merchantSn={}, acquirer={}, ruleName={}, " +
                "maxCount=0, ruleMatched=false, " +
                "oldAccountInfo={}, newAccountInfo={}",
                merchantSn, acquirer, rule.getRuleName(),
                formatAccountInfo(oldAccountInfo), formatAccountInfo(newAccountInfo));
    }
    
    /**
     * 记录次数限制规则阻止的日志
     */
    private void logCountLimitBlocked(String merchantSn, String acquirer, AcquirerAccountChangeRuleDO rule,
                                      int currentCount, AccountChangeValidationResult result,
                                      BankAccountSimpleInfoBO oldAccountInfo, BankAccountSimpleInfoBO newAccountInfo) {
        log.warn("Account change blocked due to max_count_exceeded, " +
                "merchantSn={}, acquirer={}, ruleName={}, " +
                "periodDays={}, maxCount={}, currentCount={}, " +
                "nextChangeTime={}, oldAccountInfo={}, newAccountInfo={}",
                merchantSn, acquirer, rule.getRuleName(),
                rule.getPeriodDays(), rule.getMaxCount(), currentCount,
                result.getNextChangeTime(),
                formatAccountInfo(oldAccountInfo), formatAccountInfo(newAccountInfo));
    }
    
    /**
     * 记录次数限制规则允许的日志
     */
    private void logCountLimitAllowed(String merchantSn, String acquirer, AcquirerAccountChangeRuleDO rule,
                                      int currentCount) {
        log.info("Account change validation passed, reason=within_limit, " +
                "merchantSn={}, acquirer={}, ruleName={}, " +
                "periodDays={}, maxCount={}, currentCount={}, remainingCount={}",
                merchantSn, acquirer, rule.getRuleName(),
                rule.getPeriodDays(), rule.getMaxCount(), currentCount, 
                rule.getMaxCount() - currentCount);
    }
    
    /**
     * 判断记录是否应该计数
     */
    private boolean shouldCountRecord(AcquirerAccountChangeRecordDO record, AcquirerAccountChangeRuleDO rule) {
        boolean shouldCount = ruleEngine.evaluate(record, rule.getRuleLogic());
        
        if (shouldCount) {
            // 记录规则命中情况
            log.info("Rule matched, record will be counted, " +
                    "merchantSn={}, acquirer={}, ruleName={}, " +
                    "recordId={}, changeTime={}, ruleLogic={}",
                    record.getMerchantSn(), record.getAcquirer(), rule.getRuleName(),
                    record.getId(), record.getCompleteTime(), rule.getRuleLogic());
        } else {
            // 记录规则不匹配情况
            log.debug("Rule not matched, record will not be counted, " +
                    "merchantSn={}, acquirer={}, ruleName={}, recordId={}",
                    record.getMerchantSn(), record.getAcquirer(), rule.getRuleName(), record.getId());
        }
        
        return shouldCount;
    }
    
    /**
     * 日期加减天数
     */
    private Date addDays(Date date, int days) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return Date.from(localDate.plusDays(days).atStartOfDay(ZoneId.systemDefault()).toInstant());
    }
    
    /**
     * 格式化账户信息用于日志输出
     * 
     * @param accountInfo 账户信息
     * @return 格式化后的字符串
     */
    private String formatAccountInfo(BankAccountSimpleInfoBO accountInfo) {
        if (accountInfo == null) {
            return "null";
        }
        try {
            return JSON.toJSONString(accountInfo);
        } catch (Exception e) {
            log.warn("Format account info error", e);
            return "format_failed";
        }
    }
    

    
    /**
     * 将状态码转换为可读文本
     * 
     * @param status 状态码
     * @return 状态文本
     */
    private String getStatusText(Integer status) {
        if (status == null) {
            return "未知";
        }
        
        if (AcquirerAccountChangeStatusEnum.PROCESSING.getValue().equals(status)) {
            return "处理中";
        } else if (AcquirerAccountChangeStatusEnum.SUCCESS.getValue().equals(status)) {
            return "成功";
        } else if (AcquirerAccountChangeStatusEnum.FAILED.getValue().equals(status)) {
            return "失败";
        } else {
            return "未知状态(" + status + ")";
        }
    }
    
    /**
     * 从JSON字符串格式化账户信息
     * 
     * @param accountInfoJson JSON字符串
     * @return 格式化后的字符串
     */
    private String formatAccountInfoFromJson(String accountInfoJson) {
        if (accountInfoJson == null || accountInfoJson.trim().isEmpty()) {
            return "null";
        }
        
        try {
            BankAccountSimpleInfoBO accountInfo = JSON.parseObject(accountInfoJson, BankAccountSimpleInfoBO.class);
            return formatAccountInfo(accountInfo);
        } catch (Exception e) {
            log.debug("Parse account info JSON error: {}", accountInfoJson, e);
            // 解析失败时，直接返回原始JSON
            return accountInfoJson;
        }
    }

    /**
     * 判断商户是否在白名单中
     */
    private boolean isInWhitelist(String merchantSn) {
        try {
            List<String> whitelist = applicationApolloConfig.getAcquirerChangeCardWhitelist();
            return whitelist != null && whitelist.contains(merchantSn);
        } catch (Exception e) {
            log.warn("获取白名单配置失败，merchantSn: {}", merchantSn, e);
            return false;
        }
    }

    /**
     * 将Map转换为BankAccountCheckInfo
     */
    private BankAccountSimpleInfoBO convertMapToBankAccountInfo(Map<?, ?> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        BankAccountSimpleInfoBO info = new BankAccountSimpleInfoBO();
        info.setCardNumber(String.valueOf(map.get(MerchantBankAccount.NUMBER)));
        Object typeObj = map.get(MerchantBankAccount.TYPE);
        info.setType(typeObj == null ? null : Integer.valueOf(String.valueOf(typeObj)));
        info.setHolder(String.valueOf(map.get(MerchantBankAccount.HOLDER)));
        return info;
    }
}