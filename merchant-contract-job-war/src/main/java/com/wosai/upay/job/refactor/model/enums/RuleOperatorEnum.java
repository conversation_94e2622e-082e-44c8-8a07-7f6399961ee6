package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 规则操作符枚举
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public enum RuleOperatorEnum implements ITextValueEnum<String> {

    /**
     * 等于
     */
    EQUALS("=", "等于"),
    EQUALS_ALIAS1("==", "等于"),
    EQUALS_ALIAS2("eq", "等于"),

    /**
     * 不等于
     */
    NOT_EQUALS("!=", "不等于"),
    NOT_EQUALS_ALIAS1("<>", "不等于"),
    NOT_EQUALS_ALIAS2("ne", "不等于"),

    /**
     * 大于
     */
    GREATER_THAN(">", "大于"),
    GREATER_THAN_ALIAS("gt", "大于"),

    /**
     * 大于等于
     */
    GREATER_THAN_OR_EQUAL(">=", "大于等于"),
    GREATER_THAN_OR_EQUAL_ALIAS("ge", "大于等于"),

    /**
     * 小于
     */
    LESS_THAN("<", "小于"),
    LESS_THAN_ALIAS("lt", "小于"),

    /**
     * 小于等于
     */
    LESS_THAN_OR_EQUAL("<=", "小于等于"),
    LESS_THAN_OR_EQUAL_ALIAS("le", "小于等于"),

    /**
     * 包含于列表
     */
    IN("in", "包含于"),

    /**
     * 不包含于列表
     */
    NOT_IN("not_in", "不包含于"),

    /**
     * 包含
     */
    CONTAINS("contains", "包含"),

    /**
     * 不包含
     */
    NOT_CONTAINS("not_contains", "不包含"),

    /**
     * 为空
     */
    IS_NULL("null", "为空"),
    IS_NULL_ALIAS("is_null", "为空"),

    /**
     * 不为空
     */
    IS_NOT_NULL("not_null", "不为空"),
    IS_NOT_NULL_ALIAS("is_not_null", "不为空");

    private final String value;
    private final String text;

    RuleOperatorEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public String getValue() {
        return this.value;
    }

    /**
     * 根据操作符字符串获取枚举
     */
    public static RuleOperatorEnum fromValue(String value) {
        for (RuleOperatorEnum operator : RuleOperatorEnum.values()) {
            if (operator.getValue().equals(value)) {
                return operator;
            }
        }
        return null;
    }
}