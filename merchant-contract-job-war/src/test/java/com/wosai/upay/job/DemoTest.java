package com.wosai.upay.job;

import com.wosai.upay.job.model.dto.acquirePos.PosResultResp;
import com.wosai.upay.job.model.dto.acquirePos.fuyou.fee.FuyouInnerCardFee;
import com.wosai.upay.job.model.dto.acquirePos.fuyou.req.FuyouElecContractQueryReq;
import com.wosai.upay.job.model.dto.acquirePos.fuyou.req.FuyouForeignCardElecContractApplyReq;
import com.wosai.upay.job.model.dto.acquirePos.fuyou.req.FuyouInnerCardElecContractApplyReq;
import com.wosai.upay.job.model.dto.acquirePos.fuyou.resp.FuyouElecContractApplyResp;
import com.wosai.upay.job.model.dto.acquirePos.resp.BaseElecContractApplyResp;
import com.wosai.upay.job.model.dto.acquirePos.resp.BaseElecContractQueryResp;
import com.wosai.upay.job.refactor.dao.InternalScheduleMainTaskDAO;
import com.wosai.upay.job.refactor.mapper.InternalScheduleMainTaskMapper;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV3Task;
import com.wosai.upay.job.service.T9Service;
import com.wosai.upay.merchant.contract.model.fuyou.AuthTypeEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.validation.Valid;
import java.util.Optional;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2025/8/25 16:12
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DemoTest {


    @Autowired
    private BusinessLicenceCertificationV3Task businessLicenceCertificationV3Task;

    @Autowired
    private InternalScheduleMainTaskDAO internalScheduleMainTaskDAO;

    @Test
    public void test() {
        final Optional<InternalScheduleMainTaskDO> internalScheduleMainTaskDO = internalScheduleMainTaskDAO.getByPrimaryKey(27260L);
//        businessLicenceCertificationV3Task.mainTaskPostProcessor(internalScheduleMainTaskDO.get(),null);
    }


    @Autowired
    T9Service t9Service;

    @Test
    public void test1() {
        @Valid FuyouElecContractQueryReq fuyouElecContractQueryReq = new FuyouElecContractQueryReq();
        fuyouElecContractQueryReq.setModifyNo("681832117");
        fuyouElecContractQueryReq.setContractNo("T744798413024342049047");
        fuyouElecContractQueryReq.setMerchantId("9a8f51da-ac4a-42e2-839f-dd40d22fbb6f");
        fuyouElecContractQueryReq.setDevCode("EK6NH2M5ZFQA");



//        final PosResultResp<? extends BaseElecContractQueryResp> posResultResp = t9Service.queryFuyouElecContractStatus(fuyouElecContractQueryReq);
//        System.out.println(posResultResp);

    }
}
