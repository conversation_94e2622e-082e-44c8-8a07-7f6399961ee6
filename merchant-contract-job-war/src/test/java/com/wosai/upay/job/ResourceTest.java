package com.wosai.upay.job;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.biz.BusinessCategory;
import com.wosai.upay.job.biz.DataBusBiz;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.service.SubtaskResultService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * ResourceTest
 *
 * <AUTHOR>
 * @date 2019-08-09 16:02
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ResourceTest {

    @Autowired
    private BusinessCategory businessCategory;

    @Autowired
    DataBusBiz biz;

    @Test
    public void getBusinessCategory() {
        System.out.println(JSON.toJSONString(businessCategory.getWeixinBusinessCategory()));
    }

    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Test
    public void insertPayForVerifyEvent() {
//        biz.insertPayForEvent("xx", "xx",1);
        final ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(44291877390L);
        final long time = contractTask.getCreate_at().getTime();
        System.out.println(time);
    }

    @Autowired
    SubtaskResultService subtaskResultService;

    @Test
    public void getAcquireSubTask(){
        subtaskResultService.getAcquireSubTask(24751083L);
    }
}
