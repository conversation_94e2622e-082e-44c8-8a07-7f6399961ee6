package com.wosai.upay.job.refactor.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.utils.thread.CollectionWorker;
import com.wosai.assistant.response.UserBean;
import com.wosai.assistant.service.UserRpcService;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.BusinessRuleBiz;
import com.wosai.upay.job.model.NetInRuleGroups;
import com.wosai.upay.job.refactor.mapper.MerchantProviderParamsDynamicMapper;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.Deleted;
import com.wosai.upay.job.refactor.service.impl.McRulesDecisionServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 规则决策测试类V2
 *
 * <AUTHOR>
 * @date 2024/08/21 16:59
 */
@Slf4j
@ActiveProfiles("dev")
public class McRulesDecisionServiceV2Test extends BaseTest {

    @Resource
    private McRulesDecisionServiceImpl mcRulesDecisionService;


    private final MerchantFeatureBO merchantFeatureBO = new MerchantFeatureBO();

    @Before
    public void initMerchant() {
        merchantFeatureBO.setMerchantSn("**************");
        merchantFeatureBO.setMerchantSn("test001");
        merchantFeatureBO.setSettlementAccountType("3");
        MerchantFeatureBO.ExtraFeature extraFeature = merchantFeatureBO.new ExtraFeature();
        MerchantFeatureBO.ExtraFeature.AccountInfo accountInfo = extraFeature.new AccountInfo();
        accountInfo.setAccountType(2);
        accountInfo.setIdentityId("540127198108113435");
        accountInfo.setHolderName("齐天大圣");
        MerchantBusinessLicenseInfo merchantBusinessLicenseInfo = new MerchantBusinessLicenseInfo();
        merchantBusinessLicenseInfo.setLegal_person_id_number("540127198108113435");
        merchantBusinessLicenseInfo.setName("");
        merchantBusinessLicenseInfo.setType(2);
        extraFeature.setAccountInfo(accountInfo);
        extraFeature.setMerchantBusinessLicenseInfo(merchantBusinessLicenseInfo);
        merchantFeatureBO.setExtraFeature(extraFeature);
    }


    /**
     * or规则测试
     */
    @Test
    public void testNestRule2() {
        merchantFeatureBO.setName("上海望德文化艺术有限公司");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS.size()).isEqualTo(1);
        assertThat(detailDOS.iterator().next().getGroupId()).isEqualTo("fuyou");
    }

    /**
     * or规则测试
     */
    @Test
    public void testNestRule3() {
        merchantFeatureBO.setPromotionOrganizationPath("85000,98948,98949,107973,3545555");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS.size()).isEqualTo(2);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("lklorg");
        assertThat(iterator.next().getGroupId()).isEqualTo("haike");
    }

    /**
     * haike unable
     */
    @Test
    public void testNestRule4() {
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setName("xxx公司");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        if (CollectionUtils.isNotEmpty(detailDOS)) {
            assertThat(detailDOS.stream().map(GroupCombinedStrategyDetailDO::getGroupId).collect(Collectors.toSet())).doesNotContain("haike");
        }
    }

    /**
     * lklorg unable
     */
    @Test
    public void testNestRule5() {
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setName("中国移动有限xxxx");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        if (CollectionUtils.isNotEmpty(detailDOS)) {
            assertThat(detailDOS.stream().map(GroupCombinedStrategyDetailDO::getGroupId).collect(Collectors.toSet())).doesNotContain("lklorg");
        }
    }

    /**
     * only can
     */
    @Test
    public void testNestRule6() {
        merchantFeatureBO.setPersonalCertificateType("1");
        merchantFeatureBO.setName("CS商户FY");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS.size()).isEqualTo(1);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("fuyou");
    }

    /**
     * only can
     */
    @Test
    public void testNestRule7() {
        merchantFeatureBO.setPromotionOrganizationPath("85000,98948,98950,104910.243343");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS.size()).isEqualTo(1);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("lklorg");
    }

    /**
     * can
     */
    @Test
    public void testNestRule8() {
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setIndustry("76d27502-312d-11e6-aebb-ecf4bbdee2f0");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS.size()).isEqualTo(1);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("haike");
    }

    /**
     * can
     */
    @Test
    public void testNestRule9() {
        merchantFeatureBO.setPromotionOrganizationPath("85000,98948,98950,104910.243343");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS.size()).isEqualTo(1);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("lklorg");
    }

    /**
     * can
     */
    @Test
    public void testNestRule10() {
        merchantFeatureBO.setPersonalCertificateType("1");
        merchantFeatureBO.setCityCode("440900");
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setPromotionOrganizationPath("2343243");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS.size()).isEqualTo(2);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("fuyou");
        assertThat(iterator.next().getGroupId()).isEqualTo("lklorg");
    }

    /**
     * checkMerchantEligibilityToAcquirer
     */
    @Test
    public void testNestRule11() {
        merchantFeatureBO.setType("1");
        merchantFeatureBO.setName("中国移动有限xxxx");
        ContractGroupRuleVerifyResultBO result = mcRulesDecisionService.checkEligibilityByUnableRule(merchantFeatureBO, AcquirerTypeEnum.LKL_V3.getValue());
        assertThat(result.isCheckPass()).isEqualTo(false);
    }

    @Test
    public void testDefault() {
    }


    @Resource
    private MerchantProviderParamsDynamicMapper paramsDynamicMapper;

    @Test
    public void multiTest() {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .select(MerchantProviderParamsDO::getMerchantSn)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .ge(MerchantProviderParamsDO::getMerchantSn, "mch-1680001575858")
                .orderByDesc(MerchantProviderParamsDO::getMerchantSn)
                .last("limit 1000");
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = paramsDynamicMapper.selectList(lambdaQueryWrapper);
        CollectionWorker.of(merchantProviderParamsDOS).forEach(merchantProviderParamsDO -> {
            String merchantSn = merchantProviderParamsDO.getMerchantSn();
            try {
                Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupBySnAndOrg(merchantSn, null).get_1();
                log.info("商户:{}, 选择的收单机构: {}", merchantSn, detailDOS);
            } catch (Exception e) {
                log.error("exception: ", e);
            }
        });
    }


}
