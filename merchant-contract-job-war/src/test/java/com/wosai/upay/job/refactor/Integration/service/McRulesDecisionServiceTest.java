package com.wosai.upay.job.refactor.Integration.service;

import avro.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.model.page.NormalPagingResult;
import com.shouqianba.cua.utils.json.JSON;
import com.shouqianba.cua.utils.object.DateExtensionUtils;
import com.shouqianba.cua.utils.thread.CollectionWorker;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.BusinessRuleBiz;
import com.wosai.upay.job.model.NetInRuleGroups;
import com.wosai.upay.job.model.dto.request.*;
import com.wosai.upay.job.model.dto.response.GroupRouteRuleDetailRspDTO;
import com.wosai.upay.job.model.dto.response.GroupRouteRulesDecisionRspDTO;
import com.wosai.upay.job.model.dto.response.McRuleGroupRspDTO;
import com.wosai.upay.job.refactor.mapper.MerchantProviderParamsDynamicMapper;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.Deleted;
import com.wosai.upay.job.refactor.service.impl.GroupRouteRulesDecisionServiceImpl;
import com.wosai.upay.job.refactor.service.impl.McRulesDecisionServiceImpl;
import com.wosai.upay.job.refactor.service.localcache.McRulesLocalCacheService;
import com.wosai.upay.job.service.AcquirerService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Sets;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import java.text.ParseException;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 规则决策测试类
 * 现有规则:
 *          1 宁波市不能入拉卡拉 (330200)
 *          2 推广组织00069不能进fuyou
 *          3 拉卡拉组织商户只能走lkl (999999)
 *          4 商户名称包含`南京莜面村餐饮管理有限公司`进ums
 *          5 "山西或者贵州, 主fuyou 辅lkl"  (140000, 520000)
 *          6 宁波市进fuyou (330200)
 *          7 兜底拉卡拉
 *
 * <AUTHOR>
 * @date 2023/11/29 16:59
 */
@Slf4j
//@ActiveProfiles("dev")
public class McRulesDecisionServiceTest extends BaseTest {

    @Resource
    private McRulesDecisionServiceImpl mcRulesDecisionService;


    private final MerchantFeatureBO merchantFeatureBO = new MerchantFeatureBO();

    @Before
    public void initMerchant() {
        merchantFeatureBO.setMerchantSn("test001");
        MerchantFeatureBO.ExtraFeature extraFeature = merchantFeatureBO.new ExtraFeature();
        MerchantFeatureBO.ExtraFeature.AccountInfo accountInfo = extraFeature.new AccountInfo();
        accountInfo.setAccountType(1);
        accountInfo.setIdentityId("540127198108113435");
        accountInfo.setHolderName("齐天大圣");
        MerchantBusinessLicenseInfo merchantBusinessLicenseInfo = new MerchantBusinessLicenseInfo();
        merchantBusinessLicenseInfo.setLegal_person_id_number("540127198108113435");
        merchantBusinessLicenseInfo.setName("");
        merchantBusinessLicenseInfo.setType(1);
        extraFeature.setAccountInfo(accountInfo);
        extraFeature.setMerchantBusinessLicenseInfo(merchantBusinessLicenseInfo);
        merchantFeatureBO.setExtraFeature(extraFeature);
    }



    /**
     * 宁波市不能入拉卡拉 (330200),进fuyou
     */
    @Test
    public void testNestRule1() {
        merchantFeatureBO.setCityCode("330200");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        for (GroupCombinedStrategyDetailDO detailDO : detailDOS) {
            assertThat(detailDO.getGroupId()).isNotEqualTo("lklorg");
            assertThat(detailDO.getGroupId()).isEqualTo("fuyou");
        }
    }

    @Test
    public void testLklBrandMode() {
        // lklorg
        merchantFeatureBO.setAcquirer("lklV3");
        merchantFeatureBO.setPaymentMode("2");
        merchantFeatureBO.setSettlementAccountType("1");
        MerchantFeatureBO.ExtraFeature extraFeature = merchantFeatureBO.new ExtraFeature();
        extraFeature.setMerchantBusinessLicenseInfo(new MerchantBusinessLicenseInfo().setType(0));
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        Assert.assertEquals(1, detailDOS.size());
        Assert.assertEquals("lklorg", detailDOS.iterator().next().getGroupId());

        // lkl_wx
        merchantFeatureBO.setAcquirer("lklV3");
        merchantFeatureBO.setPaymentMode("3");
        merchantFeatureBO.setSettlementAccountType("1");
        detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        Assert.assertEquals(1, detailDOS.size());
        Assert.assertEquals("lkl_wx", detailDOS.iterator().next().getGroupId());

        // lkl_ali
        merchantFeatureBO.setAcquirer("lklV3");
        merchantFeatureBO.setPaymentMode("4");
        merchantFeatureBO.setSettlementAccountType("1");
        detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        Assert.assertEquals(1, detailDOS.size());
        Assert.assertEquals("lkl_ali", detailDOS.iterator().next().getGroupId());

        // lkl_wx_ali
        merchantFeatureBO.setAcquirer("lklV3");
        merchantFeatureBO.setPaymentMode("5");
        merchantFeatureBO.setSettlementAccountType("1");
        detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        Assert.assertEquals(1, detailDOS.size());
        Assert.assertEquals("lkl_wx_ali", detailDOS.iterator().next().getGroupId());
    }

    @Test
    public void testHaikeBrandMode() {
        // haike
        merchantFeatureBO.setAcquirer("haike");
        merchantFeatureBO.setPaymentMode("2");
        merchantFeatureBO.setSettlementAccountType("1");
        MerchantFeatureBO.ExtraFeature extraFeature = merchantFeatureBO.new ExtraFeature();
        extraFeature.setMerchantBusinessLicenseInfo(new MerchantBusinessLicenseInfo().setType(0));
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        Assert.assertEquals(1, detailDOS.size());
        Assert.assertEquals("haike", detailDOS.iterator().next().getGroupId());

        // haike_wx
        merchantFeatureBO.setAcquirer("haike");
        merchantFeatureBO.setPaymentMode("3");
        merchantFeatureBO.setSettlementAccountType("1");
        detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        Assert.assertEquals(1, detailDOS.size());
        Assert.assertEquals("haike_wx", detailDOS.iterator().next().getGroupId());

        // haike_ali
        merchantFeatureBO.setAcquirer("haike");
        merchantFeatureBO.setPaymentMode("4");
        merchantFeatureBO.setSettlementAccountType("1");
        detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        Assert.assertEquals(1, detailDOS.size());
        Assert.assertEquals("haike_ali", detailDOS.iterator().next().getGroupId());

        // haike_wx_ali
        merchantFeatureBO.setAcquirer("haike");
        merchantFeatureBO.setPaymentMode("5");
        merchantFeatureBO.setSettlementAccountType("1");
        detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        Assert.assertEquals(1, detailDOS.size());
        Assert.assertEquals("haike_wx_ali", detailDOS.iterator().next().getGroupId());
    }

    /**
     * 推广组织00069(大客户)不能进fuyou
     */
    @Test
    public void testNestRule2() {
        merchantFeatureBO.setPromotionOrganizationPath("00069");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS).isNotEmpty();
        for (GroupCombinedStrategyDetailDO detailDO : detailDOS) {
            assertThat(detailDO.getGroupId()).isNotEqualTo("fuyou");
        }
        log.info("details: {}", detailDOS);
    }

    /**
     *  商户名称包含`南京莜面村餐饮管理有限公司`进ums
     */
    @Test
    public void testNestRule3() {
        merchantFeatureBO.setName("南京莜面村餐饮管理有限公司啦啦啦");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS).hasSize(1);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("ums");
    }

    /**
     * "山西或者贵州, 主fuyou 辅lkl"  (140000, 520000)
     */
    @Test
    public void testNestRule4() {
        merchantFeatureBO.setProvinceCode("140000");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS).hasSize(2);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("fuyou");
        assertThat(iterator.next().getGroupId()).isEqualTo("lklorg");
    }

    /**
     * 宁波市进fuyou (330200)
     */
    @Test
    public void testNestRule5() {
        merchantFeatureBO.setCityCode("330200");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS).hasSize(1);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("fuyou");
    }

    /**
     * 兜底拉卡拉
     */
    @Test
    public void testNestRule6() {
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO).get_1();
        assertThat(detailDOS).hasSize(1);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("lklorg");
    }

    @Autowired
    private BusinessRuleBiz businessRuleBiz;

    @Test
    public void testNestRule8() {
        NetInRuleGroups groups = businessRuleBiz.getRuleGroupId("21690003761673", null);
        assertThat(groups).isNotNull();
    }

    @Resource
    private AcquirerService acquirerService;

    @Test
    public void testUnable() {
        MerchantFeatureReqDTO merchantFeatureBO1 = new MerchantFeatureReqDTO();
        merchantFeatureBO1.setCityCode("310100");
        merchantFeatureBO1.setProvinceCode("310000");
        merchantFeatureBO1.setDistrictCode("310112");
        acquirerService.checkEligibilityToGroupId(merchantFeatureBO1, "fuyou", AcquirerTypeEnum.FU_YOU);
    }

    @Test
    public void testNestRule7() {
        String merchantSn = "mch-1680001206566";
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupBySnAndOrg(merchantSn, null).get_1();
        assertThat(detailDOS).isNotEmpty();
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("lklorg");
    }

    @Resource
    private MerchantProviderParamsDynamicMapper paramsDynamicMapper;

    // @Test
    public void test() {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .select(MerchantProviderParamsDO::getMerchantSn)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .gt(MerchantProviderParamsDO::getMerchantSn, "1582707488703")
                .orderByDesc(MerchantProviderParamsDO::getMerchantSn)
                .last("limit 1000");
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = paramsDynamicMapper.selectList(lambdaQueryWrapper);
        CollectionWorker.of(merchantProviderParamsDOS).forEach(merchantProviderParamsDO -> {
            String merchantSn = merchantProviderParamsDO.getMerchantSn();
            try {
                Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupBySnAndOrg(merchantSn, null).get_1();
                log.info("商户:{}, 选择的收单机构: {}", merchantSn, detailDOS);
            } catch (Exception e) {
                log.error("exception: ", e);
            }
        });
    }

    @Resource
    private McRulesLocalCacheService mcRulesLocalCacheService;


    @Resource
    private GroupRouteRulesDecisionServiceImpl groupRouteRulesDecisionService;

    @Test
    public void testListAllTopNodeGroupRouteRules() {
        List<GroupRouteRulesDecisionRspDTO> rspDTOList = groupRouteRulesDecisionService.listAllTopNodeGroupRouteRules();
        assertThat(rspDTOList).isNotNull().isNotEmpty();
        for (GroupRouteRulesDecisionRspDTO rspDTO : rspDTOList) {
            // parentId为0，因为它们是顶级节点
            assertThat(rspDTO.getParentId()).isZero();
            assertThat(rspDTO.getId()).isNotNull();
            assertThat(rspDTO.getName()).isNotBlank();
        }
        log.info(JSON.toJSONString(rspDTOList));
    }

    @Test
    public void testListGroupRouteRulesById() {
        GroupRouteRulesDecisionRspDTO res = groupRouteRulesDecisionService.getGroupRouteRulesDecisionById(260L);
        assertThat(res).isNotNull();
        log.info(JSON.toJSONString(res));
    }

    @Test
    public void testGetGroupRouteRuleDetailById() {
        GroupRouteRuleDetailRspDTO groupRouteRuleDetail= groupRouteRulesDecisionService.getGroupRouteRuleDetailById(2L);
        assertThat(groupRouteRuleDetail).isNotNull();
        assertThat(groupRouteRuleDetail.getObjectPropertyValue()).isEqualTo("00069");
    }

    @Test
    public void testDeleteGroupRouteRuleById() {
        Integer deleteRows = groupRouteRulesDecisionService.deleteGroupRouteRuleById(201L);
        assertThat(deleteRows).isNotNull().isPositive();
    }

    @Test
    public void testInsertGroupRouteRuleDetail() {
        GroupRouteRuleDetailRepDTO groupRouteRuleDetailReqDTO = getGroupRouteRuleDetailRepDTO(777L, "cityCode", "EQUAL", "344542");
        Integer insertRows = groupRouteRulesDecisionService.insertGroupRouteRuleDetail(groupRouteRuleDetailReqDTO);
        assertThat(insertRows).isNotNull().isPositive();
    }

    @Test
    public void testUpdateGroupRouteRuleDetail() {
        GroupRouteRuleDetailRepDTO groupRouteRuleDetailReqDTO = new GroupRouteRuleDetailRepDTO();
        groupRouteRuleDetailReqDTO.setId(259L);
        groupRouteRuleDetailReqDTO.setRuleDecisionId(260L);
        groupRouteRuleDetailReqDTO.setObjectPropertyType("cityCode");
        groupRouteRuleDetailReqDTO.setLogicalOperationType("IN");
        // groupRouteRuleDetailReqDTO.setObjectPropertyValue("3434534");
        //String value = "[\"34545\"]";
        String value = "[\"\"]";
        //String value = "3456";
        groupRouteRuleDetailReqDTO.setObjectPropertyValue(Lists.newArrayList("5645", "42343", "3423"));
        groupRouteRuleDetailReqDTO.setValidStatus(1);
        groupRouteRuleDetailReqDTO.setUpdateBy("马苏杭");
        groupRouteRuleDetailReqDTO.setCreateBy("马苏杭");
        Integer updateRows = groupRouteRulesDecisionService.updateGroupRouteRuleDetail(groupRouteRuleDetailReqDTO);
        assertThat(updateRows).isNotNull().isPositive();
    }

    @Test
    public void testInsertGroupRouteRule() {
        GroupRouteRulesDecisionReqDTO topDecision = getGroupRouteRulesDecisionReqDTO("Top Decision", "OR", null, "ENABLE");
        topDecision.setPriority(777);
        topDecision.setParentId(0L);
        GroupCombinedStrategyRepDTO strategyRepDTO = new GroupCombinedStrategyRepDTO();
        strategyRepDTO.setAcquirer("tonglianV2");
        strategyRepDTO.setGroupId("jay");
        strategyRepDTO.setGroupType(1);
        GroupCombinedStrategyRepDTO strategyRepDTO1 = new GroupCombinedStrategyRepDTO();
        strategyRepDTO1.setAcquirer("lklV3");
        strategyRepDTO1.setGroupId("lklorg");
        strategyRepDTO1.setGroupType(2);
        topDecision.setGroupCombinedStrategyType(2);
        topDecision.setGroupCombinedStrategyRepDTOList(Lists.newArrayList(strategyRepDTO, strategyRepDTO1));

        GroupRouteRulesDecisionReqDTO childDecision1 = getGroupRouteRulesDecisionReqDTO("Child Decision1", "AND", "AND", null);
        GroupRouteRuleDetailRepDTO ruleDetail1 = getGroupRouteRuleDetailRepDTO(null, "cityCode", "EQUAL", "330200");
        childDecision1.setGroupRouteRuleDetailRepDTOList(Lists.newArrayList(ruleDetail1));

        GroupRouteRulesDecisionReqDTO childDecision2 = getGroupRouteRulesDecisionReqDTO("Child Decision2", "AND", "AND", null);
        GroupRouteRuleDetailRepDTO ruleDetail2 = getGroupRouteRuleDetailRepDTO(null, "provinceCode", "EQUAL", "556");
        childDecision2.setGroupRouteRuleDetailRepDTOList(Lists.newArrayList(ruleDetail2));

        GroupRouteRulesDecisionReqDTO childDecision3 = getGroupRouteRulesDecisionReqDTO("Child Decision3", "AND", "AND", null);
        GroupRouteRulesDecisionReqDTO childDecision4 = getGroupRouteRulesDecisionReqDTO("Child Decision4", "AND", "AND", null);
        GroupRouteRuleDetailRepDTO ruleDetail4 = getGroupRouteRuleDetailRepDTO(null, "provinceCode", "NOT_EQUAL", "999");
        childDecision4.setGroupRouteRuleDetailRepDTOList(Lists.newArrayList(ruleDetail4));
        GroupRouteRulesDecisionReqDTO childDecision5 = getGroupRouteRulesDecisionReqDTO("Child Decision5", "AND", "AND", null);
        GroupRouteRuleDetailRepDTO ruleDetail5 = getGroupRouteRuleDetailRepDTO(null, "name", "NOT_EQUAL", "阿波罗");
        childDecision5.setGroupRouteRuleDetailRepDTOList(Lists.newArrayList(ruleDetail5));
        childDecision3.setChildrenRulesDecisionNodeList(Lists.newArrayList(childDecision4, childDecision5));

        topDecision.setChildrenRulesDecisionNodeList(Lists.newArrayList(childDecision1, childDecision2, childDecision3));

        Integer insertRows = groupRouteRulesDecisionService.insertGroupRouteRule(topDecision);
        assertThat(insertRows).isNotNull().isPositive().isEqualTo(10);

    }

    @Test
    public void testUpdateGroupRouteRule() {
        GroupRouteRulesDecisionReqDTO topDecision = getGroupRouteRulesDecisionReqDTO("Top Decision", "OR", null, "ENABLE");
        topDecision.setGroupCombinedStrategyType(1);
        GroupCombinedStrategyRepDTO strategyRepDTO = new GroupCombinedStrategyRepDTO();
        strategyRepDTO.setAcquirer("lklV3");
        strategyRepDTO.setGroupId("sqb");
        strategyRepDTO.setGroupType(1);
        topDecision.setGroupCombinedStrategyRepDTOList(Lists.newArrayList(strategyRepDTO));
        topDecision.setPriority(888);
        topDecision.setName(null);
        topDecision.setParentId(0L);
        topDecision.setId(260L);

        GroupRouteRulesDecisionReqDTO childDecision1 = getGroupRouteRulesDecisionReqDTO("Child Decision1", "AND", "AND", null);
        childDecision1.setId(261L);
        childDecision1.setName("99999999 Child Decision1-update1");
        GroupRouteRuleDetailRepDTO ruleDetail1 = getGroupRouteRuleDetailRepDTO(null, "cityCode", "EQUAL", "330200");
        ruleDetail1.setId(269L);
        ruleDetail1.setObjectPropertyValue(Lists.newArrayList("99999999-update1"));
        childDecision1.setGroupRouteRuleDetailRepDTOList(Lists.newArrayList(ruleDetail1));

        GroupRouteRulesDecisionReqDTO childDecision2 = getGroupRouteRulesDecisionReqDTO("Child Decision2", "AND", "AND", null);
        childDecision2.setId(262L);
        childDecision2.setName("99999999 Child Decision2-update1");

        GroupRouteRuleDetailRepDTO ruleDetail2 = getGroupRouteRuleDetailRepDTO(null, "provinceCode", "EQUAL", "556");
        ruleDetail2.setId(263L);
        ruleDetail2.setObjectPropertyValue(Lists.newArrayList("99999999-update1"));
        childDecision2.setGroupRouteRuleDetailRepDTOList(Lists.newArrayList(ruleDetail2));

        GroupRouteRulesDecisionReqDTO childDecision3 = getGroupRouteRulesDecisionReqDTO("99999999 Child Decision3", "AND", "AND", null);
        childDecision3.setId(265L);
        childDecision3.setName("99999999 Child Decision3-update1");

        GroupRouteRuleDetailRepDTO ruleDetail5 = getGroupRouteRuleDetailRepDTO(null, "name", "NOT_EQUAL", "阿波罗");
        //ruleDetail5.setId(273L);
        ruleDetail5.setObjectPropertyValue(Lists.newArrayList("99999999 阿波罗-update1"));
        childDecision3.setGroupRouteRuleDetailRepDTOList(Lists.newArrayList(ruleDetail5));

        topDecision.setChildrenRulesDecisionNodeList(Lists.newArrayList(childDecision1, childDecision2, childDecision3));

        Integer insertRows = groupRouteRulesDecisionService.updateGroupRouteRule(topDecision);
        assertThat(insertRows).isNotNull().isPositive();
    }

    @NotNull
    private GroupRouteRuleDetailRepDTO getGroupRouteRuleDetailRepDTO(Long ruleDecisionId, String propertyType, String logicalType,  String propertyValue) {
        GroupRouteRuleDetailRepDTO ruleDetail = new GroupRouteRuleDetailRepDTO();
        ruleDetail.setRuleDecisionId(ruleDecisionId);
        ruleDetail.setObjectPropertyType(propertyType);
        ruleDetail.setLogicalOperationType(logicalType);
        ruleDetail.setObjectPropertyValue(Lists.newArrayList(propertyValue));
        ruleDetail.setValidStatus(1);
        ruleDetail.setUpdateBy("马苏杭");
        ruleDetail.setCreateBy("马苏杭");
        return ruleDetail;
    }

    @NotNull
    private GroupRouteRulesDecisionReqDTO getGroupRouteRulesDecisionReqDTO(String name, String ruleConnectionType, String ruleDetailConnectionType, String chooseType) {
        GroupRouteRulesDecisionReqDTO childDecision = new GroupRouteRulesDecisionReqDTO();
        childDecision.setName(name);
        childDecision.setRuleConnectionType(ruleConnectionType);
        childDecision.setRuleDetailConnectionType(ruleDetailConnectionType);
        childDecision.setChooseType(chooseType);
        childDecision.setValidStatus(1);
        childDecision.setUpdateBy("马苏杭");
        childDecision.setCreateBy("马苏杭");
        return childDecision;
    }

    @Test
    public void testParseObjectPropertyValue() {
        String value = "[\"330000\",\"320000\",\"510000\",\"430000\",\"420000\",\"370000\",\"360000\",\"120000\",\"220000\", \"640000\"]";
        List<String> objectPropertyValue = groupRouteRulesDecisionService.parseObjectPropertyValue(value);
        assertThat(objectPropertyValue).hasSize(10);
        String value1 = "85000,98948,98950,104735";
        List<String> objectPropertyValue1 = groupRouteRulesDecisionService.parseObjectPropertyValue(value1);
        assertThat(objectPropertyValue1).hasSize(1);
        String value2 = "34545";
        List<String> objectPropertyValue2 = groupRouteRulesDecisionService.parseObjectPropertyValue(value2);
        assertThat(objectPropertyValue2).hasSize(1);
        String value3 = "[\"34545\"]";
        List<String> objectPropertyValue3 = groupRouteRulesDecisionService.parseObjectPropertyValue(value3);
        assertThat(objectPropertyValue3).hasSize(1);
    }

    @Test
    public void testListGroupIdsByAcquirer() {
        List<McRuleGroupRspDTO> groupIds = groupRouteRulesDecisionService.listGroupIdsByAcquirer("lklV3");
        assertThat(groupIds).isNotNull().isNotEmpty();
        log.info("groupIds: {}", groupIds);
    }

    @Test
    public void testPageDate() throws ParseException {
        GroupRouteRuleFilterReqDTO groupRouteRuleFilterReqDTO = new GroupRouteRuleFilterReqDTO();
        groupRouteRuleFilterReqDTO.setCreateStartDate(DateExtensionUtils.parseTimestamp("2024-01-12"));
        groupRouteRuleFilterReqDTO.setCreateEndDate(DateExtensionUtils.parseTimestamp("2024-04-12"));
        groupRouteRuleFilterReqDTO.setUpdateStartDate(DateExtensionUtils.parseTimestamp("2024-04-18"));
        groupRouteRuleFilterReqDTO.setUpdateEndDate(DateExtensionUtils.parseTimestamp("2024-04-17"));
        groupRouteRuleFilterReqDTO.setPageNum(1);
        groupRouteRuleFilterReqDTO.setPageSize(3);
        String jsonString = JSON.toJSONString(groupRouteRuleFilterReqDTO);
        GroupRouteRuleFilterReqDTO groupRouteRuleFilterReqDTO1 = JSON.parseObject(jsonString, GroupRouteRuleFilterReqDTO.class);
    }

    @Test
    public void testPageName() throws ParseException {
        String name = "宁波市不能入拉卡拉";
        GroupRouteRuleFilterReqDTO groupRouteRuleFilterReqDTO = new GroupRouteRuleFilterReqDTO();
        groupRouteRuleFilterReqDTO.setCreateStartDate(DateExtensionUtils.parseTimestamp("2022-01-12 13:34:54"));
        groupRouteRuleFilterReqDTO.setCreateEndDate(DateExtensionUtils.parseTimestamp("2023-12-25 13:34:54"));
        groupRouteRuleFilterReqDTO.setUpdateStartDate(DateExtensionUtils.parseTimestamp("2022-04-18 13:34:54"));
        groupRouteRuleFilterReqDTO.setUpdateEndDate(DateExtensionUtils.parseTimestamp("2024-04-07"));
        groupRouteRuleFilterReqDTO.setPageNum(1);
        groupRouteRuleFilterReqDTO.setPageSize(5);
        groupRouteRuleFilterReqDTO.setName(name);
        NormalPagingResult<GroupRouteRulesDecisionRspDTO> pagingResult = groupRouteRulesDecisionService.pageGroupRouteRules(groupRouteRuleFilterReqDTO);
        assertThat(pagingResult).isNotNull();
        assertThat(pagingResult.getTotal()).isEqualTo(1L);
        assertThat(pagingResult.getList()).isNotEmpty();
        assertThat(pagingResult.getList().get(0).getName()).isEqualTo(name);
    }

    @Test
    public void testPageChooseType() throws ParseException {
        String filter = "UNABLE";
        GroupRouteRuleFilterReqDTO groupRouteRuleFilterReqDTO = new GroupRouteRuleFilterReqDTO();
        groupRouteRuleFilterReqDTO.setCreateStartDate(DateExtensionUtils.parseTimestamp("2022-01-12"));
        groupRouteRuleFilterReqDTO.setCreateEndDate(DateExtensionUtils.parseTimestamp("2025-04-12"));
        groupRouteRuleFilterReqDTO.setUpdateStartDate(DateExtensionUtils.parseTimestamp("2022-04-18"));
        groupRouteRuleFilterReqDTO.setUpdateEndDate(DateExtensionUtils.parseTimestamp("2025-04-17"));
        groupRouteRuleFilterReqDTO.setPageNum(1);
        groupRouteRuleFilterReqDTO.setPageSize(10);
        groupRouteRuleFilterReqDTO.setChooseType(filter);
        NormalPagingResult<GroupRouteRulesDecisionRspDTO> pagingResult = groupRouteRulesDecisionService.pageGroupRouteRules(groupRouteRuleFilterReqDTO);
        assertThat(pagingResult).isNotNull();
        assertThat(pagingResult.getTotal()).isEqualTo(12L);
        assertThat(pagingResult.getList()).hasSize(10);
    }

    @Test
    public void testPageObjectPropertyFilter() throws ParseException {
        GroupRouteRuleFilterReqDTO groupRouteRuleFilterReqDTO = new GroupRouteRuleFilterReqDTO();
        groupRouteRuleFilterReqDTO.setCreateStartDate(DateExtensionUtils.parseTimestamp("2022-01-12"));
        groupRouteRuleFilterReqDTO.setCreateEndDate(DateExtensionUtils.parseTimestamp("2025-04-12"));
        groupRouteRuleFilterReqDTO.setUpdateStartDate(DateExtensionUtils.parseTimestamp("2022-04-18"));
        groupRouteRuleFilterReqDTO.setUpdateEndDate(DateExtensionUtils.parseTimestamp("2025-04-17"));
        groupRouteRuleFilterReqDTO.setPageNum(1);
        groupRouteRuleFilterReqDTO.setPageSize(10);
        MerchantObjectPropertyDTO merchantObjectPropertyDTO = new MerchantObjectPropertyDTO("cityCode", "NOT_IN", Lists.newArrayList("440900"));
        MerchantObjectPropertyDTO merchantObjectPropertyDTO1 = new MerchantObjectPropertyDTO("provinceCode", "IN", Lists.newArrayList("440000"));
        MerchantObjectPropertyDTO merchantObjectPropertyDTO2 = new MerchantObjectPropertyDTO("promotionOrganizationPath", "NOT_START_WITH", Lists.newArrayList("00069"));
        groupRouteRuleFilterReqDTO.setMerchantObjectPropertyDTOList(Lists.newArrayList(merchantObjectPropertyDTO, merchantObjectPropertyDTO1, merchantObjectPropertyDTO2));
        NormalPagingResult<GroupRouteRulesDecisionRspDTO> pagingResult = groupRouteRulesDecisionService.pageGroupRouteRules(groupRouteRuleFilterReqDTO);
        assertThat(pagingResult).isNotNull();
        assertThat(pagingResult.getList()).isNotEmpty();
    }

    @Test
    public void testUpdateStatus() {
        Integer updateRows = groupRouteRulesDecisionService.updateGroupRouteRuleStatusByIds(Lists.newArrayList(263L, 264L), 1);
        assertThat(updateRows).isNotNull().isPositive();
    }

    @Test
    public void testVerifyPriority() {
        GroupRouteRulesDecisionReqDTO dto = new GroupRouteRulesDecisionReqDTO();
        dto.setPriority(1);
        dto.setId(1L);
        groupRouteRulesDecisionService.updateGroupRouteRule(dto);
    }

}
